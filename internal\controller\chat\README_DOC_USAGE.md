# 📚 Controller层外部文档使用说明

本文档说明如何在controller控制层中使用外部文档功能，实现API接口的详细文档描述。

## 🎯 实现效果

### 原来的方式 ❌
```go
// 接口注释简单，文档信息不足
func (c *ctrlChat) ChatWsLogin(ctx context.Context, req *chat.ChatWsLoginReq) (res *chat.ChatWsLoginRes, err error) {
    // 简单的业务逻辑...
}
```

### 现在的方式 ✅
```go
// ChatWsLogin 聊天WebSocket长连接登录
// 接口描述：建立实时聊天的WebSocket长连接，支持实时消息收发
// 详细文档：参考 api/chat/docs/ws-login.md
func (c *ctrlChat) ChatWsLogin(ctx context.Context, req *chat.ChatWsLoginReq) (res *chat.ChatWsLoginRes, err error) {
    // 1. 从上下文中获取请求对象
    r := g.RequestFromCtx(ctx)
    
    // 2. 将HTTP请求升级为WebSocket连接
    ws, err := wsUpGrader.Upgrade(r.Response.Writer, r.Request, nil)
    if err != nil {
        g.Log().Error(ctx, "WebSocket升级失败:", err)
        response.Error(ctx, "WebSocket连接建立失败: "+err.Error())
        return nil, err
    }
    
    // 3. 处理WebSocket登录认证和连接管理
    err, nRet := c.server.HandelWsLogin(r, ws)
    
    // 4. 根据认证结果发送响应
    if 0 != nRet {
        response.WsError(ws, err.Error(), nRet)
        _ = ws.Close()
        g.Log().Error(ctx, "WebSocket登录失败:", err)
    } else {
        response.WsSuccess(ws, "WebSocket连接成功", nil)
        g.Log().Info(ctx, "WebSocket连接建立成功")
    }
    
    return nil, nil
}
```

## 📋 文档关联方式

### 1. API定义层（api/chat/chat.go）
```go
// 在API定义中引用外部文档
type ChatWsLoginReq struct {
    g.Meta `path:"/ws-login" tags:"聊天 WebSocket" method:"get" summary:"1、聊天长连接登录"`
    // 注意：详细的文档内容在 docs/ws-login.md 中维护
    // 字段定义...
}
```

### 2. Controller实现层（internal/controller/chat/chat.go）
```go
// ChatWsLogin 聊天WebSocket长连接登录
// 接口描述：建立实时聊天的WebSocket长连接，支持实时消息收发
// 详细文档：参考 api/chat/docs/ws-login.md
func (c *ctrlChat) ChatWsLogin(ctx context.Context, req *chat.ChatWsLoginReq) (res *chat.ChatWsLoginRes, err error) {
    // 实现逻辑...
}
```

### 3. 外部文档文件（api/chat/docs/ws-login.md）
```markdown
# 🔗 WebSocket聊天长连接登录接口

## 连接方式
**WebSocket URL：** `ws://{host}:{port}/chat/ws-login`
**认证方式：** 请求头携带 `Authorization: AyjChat <token>`

## 详细说明
...（完整的Markdown文档）
```

## 🚀 已实现的接口

### 1. WebSocket聊天登录接口
- **Controller方法**：`ChatWsLogin`
- **API路径**：`/ws-login`
- **文档文件**：`api/chat/docs/ws-login.md`
- **功能**：建立WebSocket长连接，支持实时聊天

### 2. 聊天功能测试接口
- **Controller方法**：`ChatTestApi`
- **API路径**：`/test-api`
- **文档文件**：`api/chat/docs/test-api.md`
- **功能**：测试聊天系统各项功能

### 3. 消息状态查询接口
- **Controller方法**：`MessageStatusQuery`
- **API路径**：`/message-status`
- **文档文件**：`api/chat/docs/message-status.md`
- **功能**：查询消息的详细状态信息

## 📝 Controller层最佳实践

### 1. 接口注释规范
```go
// [接口名称] [简短描述]
// 接口描述：[详细的功能描述]
// 详细文档：参考 api/chat/docs/[文档文件名].md
func (c *ctrlChat) MethodName(ctx context.Context, req *chat.RequestType) (res *chat.ResponseType, err error) {
    // 实现逻辑...
}
```

### 2. 代码结构优化
```go
// ==================== [功能模块名称] ====================

// 接口实现方法
func (c *ctrlChat) MainMethod() {
    // 1. 参数验证
    // 2. 业务逻辑处理
    // 3. 响应构造
    // 4. 日志记录
}

// ==================== 辅助方法 ====================

// 私有辅助方法
func (c *ctrlChat) helperMethod() {
    // 辅助逻辑...
}
```

### 3. 错误处理和日志
```go
func (c *ctrlChat) SomeMethod(ctx context.Context, req *chat.SomeReq) (res *chat.SomeRes, err error) {
    // 参数验证
    if req.SomeField == "" {
        return nil, fmt.Errorf("参数不能为空")
    }
    
    // 业务处理
    result, err := c.server.SomeBusinessLogic(ctx, req)
    if err != nil {
        g.Log().Error(ctx, "业务处理失败", g.Map{
            "error": err.Error(),
            "req":   req,
        })
        return nil, err
    }
    
    // 成功日志
    g.Log().Info(ctx, "操作成功", g.Map{
        "result": result,
    })
    
    return &chat.SomeRes{Data: result}, nil
}
```

## 🔧 开发工作流

### 1. 创建新接口的步骤
1. **编写文档**：在 `api/chat/docs/` 目录下创建 `.md` 文件
2. **定义API**：在 `api/chat/chat.go` 中定义请求和响应结构
3. **实现Controller**：在 `internal/controller/chat/chat.go` 中实现业务逻辑
4. **注册路由**：确保在 `internal/cmd/cmd.go` 中正确注册路由

### 2. 文档更新流程
1. **修改文档**：直接编辑 `docs/` 目录下的 `.md` 文件
2. **重新编译**：运行 `go build` 重新编译程序
3. **验证效果**：在Swagger UI中查看更新后的文档
4. **提交代码**：将文档变更提交到版本控制系统

## 📊 接口实现统计

| 接口名称 | Controller方法 | API路径 | 文档文件 | 实现状态 |
|----------|----------------|---------|----------|----------|
| WebSocket聊天登录 | ChatWsLogin | /ws-login | ws-login.md | ✅ 已实现 |
| 聊天功能测试 | ChatTestApi | /test-api | test-api.md | ✅ 已实现 |
| 消息状态查询 | MessageStatusQuery | /message-status | message-status.md | ✅ 已实现 |

## 🎉 优势总结

### ✅ 代码质量提升
- **注释规范**：统一的接口注释格式
- **结构清晰**：按功能模块组织代码
- **错误处理**：完善的错误处理和日志记录

### ✅ 文档管理优化
- **文档独立**：文档内容与代码分离
- **格式丰富**：支持Markdown格式的详细文档
- **维护方便**：技术写作人员可独立维护文档

### ✅ 开发效率提升
- **开发规范**：统一的开发模式和代码结构
- **调试便利**：详细的日志记录便于问题排查
- **测试友好**：提供专门的测试接口

### ✅ 团队协作改善
- **职责分离**：开发人员专注业务逻辑，技术写作人员专注文档
- **版本控制**：文档变更不影响代码文件
- **知识传承**：详细的文档便于新人理解和维护

这种方式完美结合了代码实现和文档管理，既保持了代码的简洁性，又提供了丰富的API文档！
