/*
******		FileName	:	message.go
******		Describe	:	此文件主要用于聊天消息表
******		Date		:	2025-04-03
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   聊天消息表相关的数据库模型
 */

package modelChat

import (
	"github.com/gogf/gf/v2/os/gtime"
	"gorm.io/gorm"
	"time"
)

// 消息体表 - 考虑到千万级用户，采用分表策略，按用户ID或会话ID分片 , 表名可以是 chat_message_{shard_id}
type ChatMessage struct {
	gorm.Model

	Id      int64  `gorm:"name:id;primary;auto:yes;unique;comment:消息ID"` // 主键，唯一，自增
	MsgType string `gorm:"name:msg_type;comment:消息类型，chat, system"`
	MsgId   string `gorm:"name:msg_id;size:32;index;comment:消息唯一ID"`        // 全局唯一消息ID，用于跨分片查询
	Status  int8   `gorm:"name:msg_status;comment:消息状态，1发送中，2已发送，3已送达，4已读"` //消息状态

	SenderId string      `gorm:"name:sender_id;size:64;index;comment:发送者ID"`       //	发现消息的id
	SendTime *gtime.Time `gorm:"name:send_time;type:timestamp;index;comment:发送时间"` // 发送消息的时间

	ReceiverId   string `gorm:"name:receiver_id;size:64;index;comment:接收者ID, 单聊为接收用户ID，群聊为群ID"` // 接受者id，单聊为接收用户ID，群聊为群ID
	ReceiverType int8   `gorm:"name:receiver_type;index;comment:接收者类型，1单聊，2群聊"`                 // 接受者，单聊为两个用户ID拼接，群聊为群ID

	MsgContentFmt  int8   `gorm:"name:msg_content_fmt;comment:内容格式，1 JSON，2 Protobuf"`
	MsgContentType string `gorm:"name:msg_content_type;type:text;comment:消息内容格式，消息类型，text，image，audio，video, file"`
	MsgContent     string `gorm:"name:msg_content;type:text;comment:消息内容，文本直接存储，其他类型存储URL"`

	Extra string `gorm:"name:extra;type:text;comment:额外信息，JSON格式"`

	// 分表字段
	ShardKey int8 `gorm:"name:shard_key;comment:分片键，用于分表"` // 可以基于会话ID的哈希值

	CreatedAt *time.Time `gorm:"name:created_at;type:timestamp;auto:no;time_format:2006-01-02 15:04:05;comment:创建时间"`
	UpdatedAt *time.Time `gorm:"name:updated_at;type:timestamp;auto:no;default:Null;time_format:2006-01-02 15:04:05;comment:更新时间"`
	DeletedAt *time.Time `gorm:"name:deleted_at;type:timestamp;time_format:2006-01-02 15:04:05;comment:删除时间"`
}

// 消息接收状态表 - 记录每条消息对每个接收者的状态
type ChatMessageReceiver struct {
	gorm.Model

	Id         int64       `gorm:"name:id;primary;auto:yes;unique;comment:ID"` // 主键，唯一，自增
	ConvId     string      `gorm:"name:conv_id;size:64;index;comment:会话ID，单聊 双方的id组合 user_id-user_id, 群聊 群id"`
	MsgId      string      `gorm:"name:msg_id;size:32;index;comment:消息唯一ID"`
	ReceiverId string      `gorm:"name:receiver_id;size:32;index;comment:接收者ID"`
	ReadTime   *gtime.Time `gorm:"name:read_time;type:timestamp;comment:已读时间"`
	Status     int8        `gorm:"name:status;index;comment:消息状态，1发送中，2已发送，3已送达，4已读"`

	// 分表字段
	ShardKey int8 `gorm:"name:shard_key;comment:分片键，用于分表"` // 可以基于接收者ID的哈希值

	CreatedAt *time.Time `gorm:"name:created_at;type:timestamp;auto:no;time_format:2006-01-02 15:04:05;comment:创建时间"`
	UpdatedAt *time.Time `gorm:"name:updated_at;type:timestamp;auto:no;default:Null;time_format:2006-01-02 15:04:05;comment:更新时间"`

	// 添加组合索引，优化查询性能
	_ string `gorm:"index:idx_receiver_msg;columns:receiver_id,msg_id;comment:接收者和消息ID的组合索引"`
	_ string `gorm:"index:idx_receiver_status;columns:receiver_id,status;comment:接收者和状态的组合索引"`
}

// 会话表 - 记录用户的所有会话
type ChatConversation struct {
	gorm.Model

	Id          int64       `gorm:"name:id;primary;auto:yes;unique;comment:主键，唯一，自增"`
	ConvId      string      `gorm:"name:conv_id;size:64;comment:会话ID，单聊 双方的id组合 user_id-user_id, 群聊 群id"`
	ConvType    int8        `gorm:"name:conv_type;comment:会话类型，1单聊，2群聊"`
	UserId      string      `gorm:"name:user_id;size:32;index;comment:会话所属用户 id"`
	TargetId    string      `gorm:"name:target_id;size:32;comment:目标ID，单聊为对方用户ID，群聊为群ID"`
	LastMsgId   string      `gorm:"name:last_msg_id;size:32;comment:最后一条消息ID"`
	LastMsgTime *gtime.Time `gorm:"name:last_msg_time;index;type:timestamp;comment:最后一条消息时间"`
	UnreadCount int         `gorm:"name:unread_count;default:0;index;comment:未读消息数"`
	Status      int8        `gorm:"name:status;default:1;index;comment:会话状态，1正常，2置顶，3免打扰，4删除"`

	// 分表字段
	//ShardKey int `gorm:"name:shard_key;comment:分片键，用于分表"` // 可以基于接收者ID的哈希值

	CreatedAt *time.Time `gorm:"name:created_at;type:timestamp;auto:no;time_format:2006-01-02 15:04:05;comment:创建时间"`
	UpdatedAt *time.Time `gorm:"name:updated_at;type:timestamp;auto:no;default:Null;time_format:2006-01-02 15:04:05;comment:更新时间"`
	DeletedAt *time.Time `gorm:"name:deleted_at;type:timestamp;time_format:2006-01-02 15:04:05;comment:删除时间"`

	// 添加组合索引，优化查询性能
	_ string `gorm:"index:idx_user_target_type;columns:user_id,target_id,conv_type;comment:用户、目标和类型的组合索引"`
	_ string `gorm:"index:idx_user_unread;columns:user_id,unread_count;comment:用户和未读数的组合索引，用于快速查找有未读消息的会话"`
}
