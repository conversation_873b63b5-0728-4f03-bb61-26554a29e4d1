# 📊 消息状态查询接口

此接口用于查询指定消息的详细状态信息，包括发送状态、接收状态、已读状态等。

## 🎯 接口功能
- **消息状态查询：** 查询单条或多条消息的状态
- **批量状态查询：** 支持一次查询多条消息的状态
- **状态历史追踪：** 查看消息状态的变更历史
- **接收者状态：** 查看群聊中每个接收者的状态

## 📋 请求参数说明

### 单条消息查询
```json
{
  "query_type": "single",
  "msg_id": "msg_123456789",
  "include_history": true,
  "include_receivers": true
}
```

### 批量消息查询
```json
{
  "query_type": "batch",
  "msg_ids": ["msg_001", "msg_002", "msg_003"],
  "include_history": false,
  "include_receivers": true
}
```

### 会话消息查询
```json
{
  "query_type": "conversation",
  "conv_id": "user_001-user_002",
  "start_time": "2025-01-20 00:00:00",
  "end_time": "2025-01-20 23:59:59",
  "limit": 100
}
```

## 📤 响应数据格式

### 消息状态详情
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "msg_id": "msg_123456789",
    "msg_type": "chat",
    "sender_id": "user_001",
    "receiver_id": "user_002",
    "receiver_type": 1,
    "send_time": "2025-01-20 10:30:00",
    "current_status": {
      "status_code": 4,
      "status_name": "已读",
      "update_time": "2025-01-20 10:32:15"
    },
    "status_history": [
      {
        "status_code": 1,
        "status_name": "发送中",
        "timestamp": "2025-01-20 10:30:00"
      },
      {
        "status_code": 2,
        "status_name": "已发送",
        "timestamp": "2025-01-20 10:30:01"
      },
      {
        "status_code": 3,
        "status_name": "已送达",
        "timestamp": "2025-01-20 10:30:05"
      },
      {
        "status_code": 4,
        "status_name": "已读",
        "timestamp": "2025-01-20 10:32:15"
      }
    ],
    "receivers": [
      {
        "receiver_id": "user_002",
        "receiver_nick": "张三",
        "status_code": 4,
        "status_name": "已读",
        "read_time": "2025-01-20 10:32:15",
        "delivered_time": "2025-01-20 10:30:05"
      }
    ]
  }
}
```

## 📊 状态码说明

| 状态码 | 状态名称 | 说明 | 适用场景 |
|--------|----------|------|----------|
| 1 | 发送中 | 消息正在发送过程中 | 所有消息类型 |
| 2 | 已发送 | 消息已成功发送到服务器 | 所有消息类型 |
| 3 | 已送达 | 消息已送达到接收者设备 | 单聊、群聊 |
| 4 | 已读 | 消息已被接收者阅读 | 单聊、群聊 |
| 11 | 对方已拉黑 | 发送失败，对方已拉黑发送者 | 单聊 |
| 12 | 对方已删除好友 | 发送失败，对方已删除好友关系 | 单聊 |
| 16 | 发送者被禁言 | 发送失败，发送者在群中被禁言 | 群聊 |
| 17 | 不是群成员 | 发送失败，发送者不是群成员 | 群聊 |
| 21 | 系统错误 | 发送失败，系统内部错误 | 所有消息类型 |

## 🔍 查询场景示例

### 场景1：检查单聊消息是否已读
```json
{
  "query_type": "single",
  "msg_id": "msg_single_001",
  "include_history": false,
  "include_receivers": true
}
```

**预期结果：** 返回消息的当前状态和接收者的阅读情况

### 场景2：群聊消息接收统计
```json
{
  "query_type": "single",
  "msg_id": "msg_group_001",
  "include_history": false,
  "include_receivers": true
}
```

**预期结果：** 返回群聊中每个成员的接收状态

### 场景3：消息状态变更追踪
```json
{
  "query_type": "single",
  "msg_id": "msg_trace_001",
  "include_history": true,
  "include_receivers": false
}
```

**预期结果：** 返回消息从发送到最终状态的完整变更历史

## ⚡ 性能优化建议

### 查询优化
- **批量查询：** 一次查询多条消息比多次单独查询效率更高
- **时间范围：** 指定合理的时间范围，避免查询过多历史数据
- **字段选择：** 根据需要选择是否包含历史记录和接收者详情

### 缓存策略
- **状态缓存：** 最新状态会被缓存，查询速度更快
- **历史缓存：** 状态历史记录会被缓存一段时间
- **批量缓存：** 批量查询结果会被整体缓存

## 🚨 注意事项

### 权限控制
1. **发送者权限：** 只能查询自己发送的消息状态
2. **接收者权限：** 只能查询发送给自己的消息状态
3. **群聊权限：** 群成员可以查询群内消息状态

### 数据保护
1. **敏感信息：** 不会返回消息内容，只返回状态信息
2. **隐私保护：** 接收者信息会根据权限进行过滤
3. **数据脱敏：** 非好友关系的用户信息会被脱敏处理

### 查询限制
1. **频率限制：** 每分钟最多查询100次
2. **数量限制：** 批量查询最多支持50条消息
3. **时间限制：** 历史查询最多支持30天内的数据

## 🛠️ 错误处理

### 常见错误码
| 错误码 | 错误信息 | 解决方案 |
|--------|----------|----------|
| 4001 | 消息不存在 | 检查消息ID是否正确 |
| 4002 | 无权限查询 | 确认是否有查询该消息的权限 |
| 4003 | 查询频率过高 | 降低查询频率，使用批量查询 |
| 4004 | 参数格式错误 | 检查请求参数格式 |
| 4005 | 查询数量超限 | 减少批量查询的消息数量 |

### 错误响应示例
```json
{
  "code": 4001,
  "message": "消息不存在",
  "data": null,
  "error_details": {
    "error_code": "MSG_NOT_FOUND",
    "error_msg": "指定的消息ID不存在或已被删除",
    "request_id": "req_20250120_001"
  }
}
```

## 📈 使用统计

### 统计指标
- **查询次数：** 每日查询次数统计
- **响应时间：** 平均响应时间监控
- **错误率：** 查询错误率统计
- **热点消息：** 被频繁查询的消息统计

### 监控告警
- **响应时间告警：** 平均响应时间超过100ms时告警
- **错误率告警：** 错误率超过1%时告警
- **频率告警：** 单用户查询频率异常时告警
