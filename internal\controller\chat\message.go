/*
******        FileName    :   message.go
******        Describe    :   此文件主要用于聊天信息的接口请求
******        Date        :   2025-04-03
******        Author      :   TangJinFei
******        Copyright   :   Guangzhou AiYunJi Inc.
******        Note        :   聊天基本信息
 */

package chat

import (
	"ayj_chat_back/api/chat"
	"ayj_chat_back/internal/public/response"
	"ayj_chat_back/internal/public/tools"
	"ayj_chat_back/internal/service/chat"
	"context"
)

// 实时聊天控制器
type CtrlMessage struct {
	server *server.ServerMessage
}

// 创建消息控制器
var CtrlMessageApi = CtrlMessage{}

// GetMsgConvList 1、会话列表-获取
func (c *CtrlMessage) GetMsgConvList(ctx context.Context, req *chat.GetMsgConvListReq) (res *chat.GetMsgConvListRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.GetMsgConvList(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// GetMsgListForConvId 2、会话消息-获取
func (c *CtrlMessage) GetMsgListForConvId(ctx context.Context, req *chat.GetMsgListForConvIdReq) (res *chat.GetMsgListForConvIdRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.GetMsgListForConvId(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// MarkConvMsgRead 3、会话消息-标记已读(消息id)
func (c *CtrlMessage) MarkConvMsgRead(ctx context.Context, req *chat.MarkConvMsgReadReq) (res *chat.MarkConvMsgReadRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.MarkConvMsgRead(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// MarkConvAllMsgRead 4、会话消息-标记已读(全部)
func (c *CtrlMessage) MarkConvAllMsgRead(ctx context.Context, req *chat.MarkConvAllMsgReadReq) (res *chat.MarkConvAllMsgReadRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.MarkConvAllMsgRead(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// MarkConvMsgRead 标记所有会话消息全部已读
/*func (c *CtrlMessage) MarkAllConvRead(ctx context.Context, req *chat.MarkAllConvReadReq) (res *chat.MarkAllConvReadRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.MarkAllConvRead(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}*/

// GetUnreadCount 5、未读消息数-获取
func (c *CtrlMessage) GetUnreadCount(ctx context.Context, req *chat.GetUnreadCountReq) (res *chat.GetUnreadCountRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.GetUnreadCount(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}
