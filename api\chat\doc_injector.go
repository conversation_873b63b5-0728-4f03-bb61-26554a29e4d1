package chat

import (
	"github.com/gogf/gf/v2/frame/g"
)

// DocInjector 文档注入器
type DocInjector struct {
	initialized bool
}

var docInjector = &DocInjector{}

// InjectDocs 注入外部文档到接口定义中
func InjectDocs() {
	if docInjector.initialized {
		return
	}

	// 注入WebSocket登录接口文档
	injectWSLoginDoc()

	// 注入测试接口文档
	injectTestApiDoc()

	// 注入消息状态查询接口文档
	injectMessageStatusDoc()

	docInjector.initialized = true
	g.Log().Info(nil, "外部文档注入完成")
}

// injectWSLoginDoc 注入WebSocket登录接口文档
func injectWSLoginDoc() {
	// 获取外部文档内容
	docContent := GetDocContent("ws-login.md")

	// 这里我们使用一个变通的方法：
	// 由于Go语言的结构体标签在编译时确定，我们无法动态修改
	// 但我们可以在运行时记录这些文档内容，供其他地方使用

	// 记录文档内容到全局变量
	WsLoginDoc = docContent

	g.Log().Debug(nil, "WebSocket登录接口文档已加载", g.Map{
		"doc_length": len(docContent),
		"preview":    getDocPreview(docContent, 100),
	})
}

// injectTestApiDoc 注入测试接口文档
func injectTestApiDoc() {
	docContent := GetDocContent("test-api.md")
	TestApiDoc = docContent

	g.Log().Debug(nil, "测试接口文档已加载", g.Map{
		"doc_length": len(docContent),
		"preview":    getDocPreview(docContent, 100),
	})
}

// injectMessageStatusDoc 注入消息状态查询接口文档
func injectMessageStatusDoc() {
	docContent := GetDocContent("message-status.md")
	MessageStatusDoc = docContent

	g.Log().Debug(nil, "消息状态查询接口文档已加载", g.Map{
		"doc_length": len(docContent),
		"preview":    getDocPreview(docContent, 100),
	})
}

// getDocPreview 获取文档预览
func getDocPreview(content string, maxLen int) string {
	if len(content) <= maxLen {
		return content
	}
	return content[:maxLen] + "..."
}

// GetApiDocumentation 获取API文档内容（供Swagger使用）
func GetApiDocumentation(apiPath string) string {
	// 确保文档已注入
	InjectDocs()

	switch apiPath {
	case "/ws-login":
		return WsLoginDoc
	case "/test-api":
		return TestApiDoc
	case "/message-status":
		return MessageStatusDoc
	default:
		return "接口文档未找到"
	}
}

// PrintAllDocs 打印所有文档内容（调试用）
func PrintAllDocs() {
	InjectDocs()

	g.Log().Info(nil, "=== 所有API文档内容 ===")

	g.Log().Info(nil, "WebSocket登录接口文档:")
	g.Log().Info(nil, WsLoginDoc)

	g.Log().Info(nil, "测试接口文档:")
	g.Log().Info(nil, TestApiDoc)

	g.Log().Info(nil, "消息状态查询接口文档:")
	g.Log().Info(nil, MessageStatusDoc)

	g.Log().Info(nil, "=== 文档内容结束 ===")
}

// ValidateDocInjection 验证文档注入是否成功
func ValidateDocInjection() map[string]bool {
	InjectDocs()

	results := map[string]bool{
		"ws-login":       len(WsLoginDoc) > 0 && !isErrorDoc(WsLoginDoc),
		"test-api":       len(TestApiDoc) > 0 && !isErrorDoc(TestApiDoc),
		"message-status": len(MessageStatusDoc) > 0 && !isErrorDoc(MessageStatusDoc),
	}

	g.Log().Info(nil, "文档注入验证结果", g.Map{
		"results": results,
	})

	return results
}

// isErrorDoc 检查是否是错误文档
func isErrorDoc(content string) bool {
	return len(content) > 10 && content[:10] == "文档加载失败"
}

// GetDocStats 获取文档统计信息
func GetDocStats() map[string]interface{} {
	InjectDocs()

	stats := map[string]interface{}{
		"total_docs": 3,
		"docs": map[string]interface{}{
			"ws-login": map[string]interface{}{
				"length":  len(WsLoginDoc),
				"loaded":  len(WsLoginDoc) > 0,
				"valid":   !isErrorDoc(WsLoginDoc),
				"preview": getDocPreview(WsLoginDoc, 50),
			},
			"test-api": map[string]interface{}{
				"length":  len(TestApiDoc),
				"loaded":  len(TestApiDoc) > 0,
				"valid":   !isErrorDoc(TestApiDoc),
				"preview": getDocPreview(TestApiDoc, 50),
			},
			"message-status": map[string]interface{}{
				"length":  len(MessageStatusDoc),
				"loaded":  len(MessageStatusDoc) > 0,
				"valid":   !isErrorDoc(MessageStatusDoc),
				"preview": getDocPreview(MessageStatusDoc, 50),
			},
		},
	}

	return stats
}
