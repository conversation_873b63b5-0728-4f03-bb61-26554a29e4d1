/*
******		FileName	:	chat.go
******		Describe	:	此文件主要用于聊天 websocket的接口
******		Date		:	2025-05-08
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   聊天 websocket
 */

package chat

import "github.com/gogf/gf/v2/frame/g"

// 长文档描述常量定义
const (
	// WebSocket聊天登录接口的详细文档
	ChatWsLoginDescription = `
<h3>🔗 WebSocket聊天长连接登录接口</h3>
<p><strong>连接地址：</strong><code>ws://{host}:{port}/chat/ws-login</code></p>
<p><strong>认证方式：</strong>请求头携带 <code>Authorization: AyjChat &lt;token&gt;</code></p>

<h4>📤 客户端发送消息格式</h4>
<pre><code>{
  "msg_type": "chat",
  "msg_client_id": "client_msg_001",
  "msg_receiver_id": "user_123",
  "msg_receiver_type": 1,
  "msg_content_fmt": 1,
  "msg_content_type": "text",
  "msg_content": "Hello World"
}</code></pre>

<h4>📥 服务端响应格式</h4>
<pre><code>{
  "code": 0,
  "message": "success",
  "data": {
    "msg_id": "msg_001",
    "msg_status": 2,
    "send_time": "2025-01-20 10:30:00",
    "sender_id": "user_456",
    "sender_nick": "张三",
    "conv_id": "user_123-user_456"
  }
}</code></pre>

<h4>🔄 消息类型说明</h4>
<ul>
  <li><strong>chat</strong>：普通聊天消息</li>
  <li><strong>chat_notice</strong>：聊天通知消息（如：用户加入群聊、退出群聊等）</li>
  <li><strong>system</strong>：系统消息（如：系统维护通知、版本更新等）</li>
  <li><strong>ping</strong>：心跳消息（保持连接活跃）</li>
</ul>

<h4>📋 接收者类型说明</h4>
<table border="1" style="border-collapse: collapse; width: 100%;">
  <tr>
    <th>类型值</th>
    <th>说明</th>
    <th>receiver_id格式</th>
    <th>示例</th>
  </tr>
  <tr>
    <td>1</td>
    <td>单聊</td>
    <td>对方用户ID</td>
    <td>user_123</td>
  </tr>
  <tr>
    <td>2</td>
    <td>群聊</td>
    <td>群组ID</td>
    <td>group_456</td>
  </tr>
</table>

<h4>⚠️ 重要注意事项</h4>
<ol>
  <li><strong>连接保持：</strong>建议每30秒发送一次ping消息保持连接</li>
  <li><strong>消息大小：</strong>单个消息内容大小限制为10MB</li>
  <li><strong>连接超时：</strong>无活动连接将在5分钟后自动断开</li>
  <li><strong>重连机制：</strong>连接断开后客户端应实现自动重连</li>
  <li><strong>消息去重：</strong>使用msg_client_id进行客户端消息去重</li>
</ol>

<h4>🚀 JavaScript使用示例</h4>
<pre><code>// 建立WebSocket连接
const ws = new WebSocket('ws://localhost:8000/chat/ws-login', [], {
  headers: {
    'Authorization': 'AyjChat your_token_here'
  }
});

// 连接成功
ws.onopen = function() {
  console.log('WebSocket连接已建立');

  // 发送单聊消息
  ws.send(JSON.stringify({
    msg_type: 'chat',
    msg_client_id: 'client_' + Date.now(),
    msg_receiver_id: 'user_123',
    msg_receiver_type: 1,
    msg_content_fmt: 1,
    msg_content_type: 'text',
    msg_content: 'Hello, 这是一条测试消息！'
  }));

  // 发送群聊消息
  ws.send(JSON.stringify({
    msg_type: 'chat',
    msg_client_id: 'client_' + Date.now(),
    msg_receiver_id: 'group_456',
    msg_receiver_type: 2,
    msg_content_fmt: 1,
    msg_content_type: 'text',
    msg_content: '大家好！'
  }));
};

// 接收消息
ws.onmessage = function(event) {
  const response = JSON.parse(event.data);
  console.log('收到消息:', response);

  if (response.code === 0) {
    console.log('消息发送成功:', response.data);
  } else {
    console.error('消息发送失败:', response.message);
  }
};

// 连接错误
ws.onerror = function(error) {
  console.error('WebSocket错误:', error);
};

// 连接关闭
ws.onclose = function() {
  console.log('WebSocket连接已关闭');
  // 实现重连逻辑
  setTimeout(() => {
    console.log('尝试重新连接...');
    // 重新建立连接
  }, 3000);
};

// 心跳保持
setInterval(() => {
  if (ws.readyState === WebSocket.OPEN) {
    ws.send(JSON.stringify({
      msg_type: 'ping',
      msg_client_id: 'ping_' + Date.now()
    }));
  }
}, 30000);</code></pre>

<h4>🐍 Python使用示例</h4>
<pre><code>import websocket
import json
import time
import threading

class ChatWebSocket:
    def __init__(self, url, token):
        self.url = url
        self.token = token
        self.ws = None

    def on_message(self, ws, message):
        response = json.loads(message)
        print(f"收到消息: {response}")

        if response.get('code') == 0:
            print(f"消息处理成功: {response.get('data')}")
        else:
            print(f"消息处理失败: {response.get('message')}")

    def on_error(self, ws, error):
        print(f"WebSocket错误: {error}")

    def on_close(self, ws, close_status_code, close_msg):
        print("WebSocket连接已关闭")

    def on_open(self, ws):
        print("WebSocket连接已建立")

        # 发送测试消息
        self.send_message('user_123', 1, 'text', 'Hello from Python!')

        # 启动心跳
        self.start_heartbeat()

    def send_message(self, receiver_id, receiver_type, content_type, content):
        message = {
            "msg_type": "chat",
            "msg_client_id": f"python_client_{int(time.time() * 1000)}",
            "msg_receiver_id": receiver_id,
            "msg_receiver_type": receiver_type,
            "msg_content_fmt": 1,
            "msg_content_type": content_type,
            "msg_content": content
        }
        self.ws.send(json.dumps(message))

    def start_heartbeat(self):
        def heartbeat():
            while self.ws and self.ws.sock and self.ws.sock.connected:
                try:
                    ping_message = {
                        "msg_type": "ping",
                        "msg_client_id": f"ping_{int(time.time() * 1000)}"
                    }
                    self.ws.send(json.dumps(ping_message))
                    time.sleep(30)
                except:
                    break

        threading.Thread(target=heartbeat, daemon=True).start()

    def connect(self):
        websocket.enableTrace(True)
        self.ws = websocket.WebSocketApp(
            self.url,
            header={"Authorization": f"AyjChat {self.token}"},
            on_open=self.on_open,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close
        )
        self.ws.run_forever()

# 使用示例
if __name__ == "__main__":
    chat_ws = ChatWebSocket(
        "ws://localhost:8000/chat/ws-login",
        "your_token_here"
    )
    chat_ws.connect()</code></pre>

<h4>📱 移动端注意事项</h4>
<ul>
  <li><strong>Android：</strong>需要在网络安全配置中允许明文传输（开发环境）</li>
  <li><strong>iOS：</strong>需要在Info.plist中配置App Transport Security</li>
  <li><strong>小程序：</strong>需要在管理后台配置WebSocket域名白名单</li>
  <li><strong>React Native：</strong>建议使用react-native-websocket库</li>
</ul>

<h4>🔧 错误码说明</h4>
<table border="1" style="border-collapse: collapse; width: 100%;">
  <tr>
    <th>错误码</th>
    <th>说明</th>
    <th>解决方案</th>
  </tr>
  <tr>
    <td>1001</td>
    <td>认证失败</td>
    <td>检查token是否正确或已过期</td>
  </tr>
  <tr>
    <td>1002</td>
    <td>消息格式错误</td>
    <td>检查JSON格式是否正确</td>
  </tr>
  <tr>
    <td>1003</td>
    <td>接收者不存在</td>
    <td>检查用户ID或群组ID是否正确</td>
  </tr>
  <tr>
    <td>1004</td>
    <td>权限不足</td>
    <td>检查是否有发送消息的权限</td>
  </tr>
  <tr>
    <td>1005</td>
    <td>消息内容过大</td>
    <td>减少消息内容大小</td>
  </tr>
</table>
`

	// 测试接口的详细文档
	ChatTestApiDescription = `
<h3>🧪 聊天功能测试接口</h3>
<p>此接口用于测试聊天系统的各项功能，包括消息发送、接收、状态更新等。</p>

<h4>🎯 测试场景</h4>
<ul>
  <li><strong>单聊测试：</strong>测试两个用户之间的消息收发</li>
  <li><strong>群聊测试：</strong>测试群组内多用户消息广播</li>
  <li><strong>消息状态测试：</strong>测试消息的发送、送达、已读状态</li>
  <li><strong>离线消息测试：</strong>测试用户离线时的消息存储和推送</li>
  <li><strong>消息类型测试：</strong>测试文本、图片、语音、视频等不同类型消息</li>
</ul>

<h4>📊 测试数据格式</h4>
<pre><code>{
  "test_type": "single_chat",
  "sender_id": "test_user_001",
  "receiver_id": "test_user_002",
  "message_count": 10,
  "message_interval": 1000,
  "content_types": ["text", "image", "audio"],
  "auto_read": true,
  "simulate_offline": false
}</code></pre>

<h4>📈 测试结果统计</h4>
<table border="1" style="border-collapse: collapse; width: 100%;">
  <tr>
    <th>指标</th>
    <th>说明</th>
    <th>期望值</th>
  </tr>
  <tr>
    <td>发送成功率</td>
    <td>成功发送的消息数量/总消息数量</td>
    <td>&gt;= 99%</td>
  </tr>
  <tr>
    <td>平均响应时间</td>
    <td>从发送到收到确认的平均时间</td>
    <td>&lt;= 100ms</td>
  </tr>
  <tr>
    <td>消息丢失率</td>
    <td>丢失的消息数量/总消息数量</td>
    <td>&lt;= 0.1%</td>
  </tr>
  <tr>
    <td>状态同步准确率</td>
    <td>状态正确同步的消息数量/总消息数量</td>
    <td>&gt;= 99.9%</td>
  </tr>
</table>

<h4>⚡ 性能测试参数</h4>
<ul>
  <li><strong>并发用户数：</strong>支持1000+并发连接</li>
  <li><strong>消息吞吐量：</strong>每秒处理10000+条消息</li>
  <li><strong>内存使用：</strong>单连接内存占用&lt;1MB</li>
  <li><strong>CPU使用率：</strong>正常负载下&lt;50%</li>
</ul>

<h4>🔍 调试信息</h4>
<p>测试过程中会输出详细的调试信息，包括：</p>
<ol>
  <li>连接建立和断开日志</li>
  <li>消息发送和接收时间戳</li>
  <li>错误信息和异常堆栈</li>
  <li>性能指标实时统计</li>
  <li>内存和CPU使用情况</li>
</ol>

<h4>🛠️ 测试工具推荐</h4>
<ul>
  <li><strong>Postman：</strong>支持WebSocket测试，可保存测试用例</li>
  <li><strong>wscat：</strong>命令行WebSocket客户端，适合自动化测试</li>
  <li><strong>Artillery：</strong>专业的WebSocket压力测试工具</li>
  <li><strong>自定义脚本：</strong>使用Python/Node.js编写专门的测试脚本</li>
</ul>
`
)



// 聊天websocket长连接 登录请求 (ws 消息结构体)
type ChatWsLoginReq struct {
	g.Meta `path:"/ws-login" tags:"聊天 WebSocket" method:"get" summary:"1、聊天长连接登录"  description:"使用方式:ws://{host}:{port}/chat/ws-login，并在请求头中携带Authorization: AyjChat <token>格式的认证信息。连接成功后，客户端可以发送和接收实时消息。<br><br>ws 客户端发送的消息格式："`

	MsgType         string `json:"msg_type" dc:"ws 消息类型: chat 聊天消息,, chat_notice 聊天通知消息, system 系统信息, ping 心跳信息" `
	MsgClientId     string `json:"msg_client_id" dc:"ws 消息发送端, 生成的消息id,后端不入库(前端自己判断使用)"`
	MsgReceiverId   string `json:"msg_receiver_id" dc:"ws 消息接收端id: 单聊(对方user_id),群聊(group_id)"`
	MsgReceiverType int    `json:"msg_receiver_type" dc:"ws 消息接收端类型: 1 单聊， 2 群聊"`

	MsgContentFmt  int         `json:"msg_content_fmt" dc:"ws 内容格式: 1 json, 2 protobuf"`
	MsgContentType string      `json:"msg_content_type" dc:"ws 内容类型: ，text，image，audio，video ,file 等(可扩展)"`
	MsgContent     interface{} `json:"msg_content" dc:" ws 消息内容: 可扩展"`
}

// WebSocket连接成功后的响应
type ChatWsLoginRes struct {
	g.Meta `mime:"application/json" example:"string" description:"ws 客户端接收的消息格式："`

	Code    int         `json:"code" dc:"0成功，其他失败"`
	Message string      `json:"message" dc:"提示消息"`
	Data    MessageInfo `json:"data"  dc:"客户端收到统一消息内容"`
}

// ==================== 测试接口 ====================

// 聊天功能测试接口请求
type ChatTestApiReq struct {
	g.Meta `path:"/test-api" tags:"聊天测试" method:"post" summary:"2、聊天功能测试接口" description:"`+ ChatTestApiDescription + `"`

	TestType        string   `json:"test_type" dc:"测试类型: single_chat 单聊测试, group_chat 群聊测试, message_status 消息状态测试, offline_message 离线消息测试, performance 性能测试" v:"required|in:single_chat,group_chat,message_status,offline_message,performance"`
	SenderId        string   `json:"sender_id" dc:"发送者用户ID" v:"required"`
	ReceiverId      string   `json:"receiver_id" dc:"接收者ID（用户ID或群组ID）" v:"required"`
	MessageCount    int      `json:"message_count" dc:"测试消息数量，默认10条" d:"10"`
	MessageInterval int      `json:"message_interval" dc:"消息发送间隔（毫秒），默认1000ms" d:"1000"`
	ContentTypes    []string `json:"content_types" dc:"消息内容类型列表: text, image, audio, video, file" d:"[\"text\"]"`
	AutoRead        bool     `json:"auto_read" dc:"是否自动标记为已读" d:"true"`
	SimulateOffline bool     `json:"simulate_offline" dc:"是否模拟离线场景" d:"false"`
}

// 聊天功能测试接口响应
type ChatTestApiRes struct {
	g.Meta `mime:"application/json"`

	Code    int                `json:"code" dc:"0成功，其他失败"`
	Message string             `json:"message" dc:"提示消息"`
	Data    ChatTestResultData `json:"data" dc:"测试结果数据"`
}

// 测试结果数据结构
type ChatTestResultData struct {
	TestId          string                 `json:"test_id" dc:"测试ID"`
	TestType        string                 `json:"test_type" dc:"测试类型"`
	StartTime       string                 `json:"start_time" dc:"测试开始时间"`
	EndTime         string                 `json:"end_time" dc:"测试结束时间"`
	Duration        int64                  `json:"duration" dc:"测试持续时间（毫秒）"`
	TotalMessages   int                    `json:"total_messages" dc:"总消息数量"`
	SuccessMessages int                    `json:"success_messages" dc:"成功发送的消息数量"`
	FailedMessages  int                    `json:"failed_messages" dc:"发送失败的消息数量"`
	SuccessRate     float64                `json:"success_rate" dc:"发送成功率（百分比）"`
	AvgResponseTime float64                `json:"avg_response_time" dc:"平均响应时间（毫秒）"`
	MaxResponseTime int64                  `json:"max_response_time" dc:"最大响应时间（毫秒）"`
	MinResponseTime int64                  `json:"min_response_time" dc:"最小响应时间（毫秒）"`
	MessageLossRate float64                `json:"message_loss_rate" dc:"消息丢失率（百分比）"`
	StatusSyncRate  float64                `json:"status_sync_rate" dc:"状态同步准确率（百分比）"`
	ErrorDetails    []ChatTestErrorDetail  `json:"error_details" dc:"错误详情列表"`
	PerformanceData ChatTestPerformanceData `json:"performance_data" dc:"性能数据"`
}

// 测试错误详情
type ChatTestErrorDetail struct {
	MessageId   string `json:"message_id" dc:"消息ID"`
	ErrorCode   int    `json:"error_code" dc:"错误码"`
	ErrorMsg    string `json:"error_msg" dc:"错误信息"`
	Timestamp   string `json:"timestamp" dc:"错误发生时间"`
	RetryCount  int    `json:"retry_count" dc:"重试次数"`
	IsResolved  bool   `json:"is_resolved" dc:"是否已解决"`
}

// 测试性能数据
type ChatTestPerformanceData struct {
	ConcurrentUsers    int     `json:"concurrent_users" dc:"并发用户数"`
	MessageThroughput  int     `json:"message_throughput" dc:"消息吞吐量（条/秒）"`
	MemoryUsage        float64 `json:"memory_usage" dc:"内存使用量（MB）"`
	CpuUsage           float64 `json:"cpu_usage" dc:"CPU使用率（百分比）"`
	NetworkLatency     float64 `json:"network_latency" dc:"网络延迟（毫秒）"`
	ConnectionCount    int     `json:"connection_count" dc:"活跃连接数"`
	DatabaseQueryTime  float64 `json:"database_query_time" dc:"数据库查询平均时间（毫秒）"`
	CacheHitRate       float64 `json:"cache_hit_rate" dc:"缓存命中率（百分比）"`
}
