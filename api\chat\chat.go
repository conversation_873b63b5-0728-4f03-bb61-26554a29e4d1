/*
******		FileName	:	chat.go
******		Describe	:	此文件主要用于聊天 websocket的接口
******		Date		:	2025-05-08
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   聊天 websocket
 */

package chat

import "github.com/gogf/gf/v2/frame/g"

// ==================== 外部文档引用功能 ====================
//
// 使用方法：
// 1. 在 docs/ 目录下创建 .md 文件
// 2. 使用 GetDocContent("filename.md") 获取转换后的HTML内容
// 3. 在接口定义中引用文档内容
//
// 优势：
// - 文档内容独立维护，支持Markdown格式
// - 代码更简洁，可读性更好
// - 支持版本控制和团队协作
// - 自动转换Markdown到HTML

// 文档内容常量（编译时生成）
var (
	// WebSocket登录接口文档
	WsLoginDoc = GetDocContent("ws-login.md")

	// 测试接口文档
	TestApiDoc = GetDocContent("test-api.md")

	// 消息状态查询接口文档
	MessageStatusDoc = GetDocContent("message-status.md")
)

// 初始化文档内容
func init() {
	// 在包初始化时加载外部文档
	InjectDocs()
}

// 聊天websocket长连接 登录请求 (ws 消息结构体)
type ChatWsLoginReq struct {
	g.Meta `path:"/ws-login" tags:"聊天 WebSocket" method:"get" summary:"1、聊天长连接登录" description:"<h3>🔗 WebSocket聊天长连接登录接口</h3><p><strong>连接地址：</strong><code>ws://{host}:{port}/chat/ws-login</code></p><p><strong>认证方式：</strong>请求头携带 <code>Authorization: AyjChat &lt;token&gt;</code></p><h4>📤 客户端发送消息格式</h4><pre><code class=\"language-json\">{\"msg_type\": \"chat\",\"msg_client_id\": \"client_msg_001\",\"msg_receiver_id\": \"user_123\",\"msg_receiver_type\": 1,\"msg_content_fmt\": 1,\"msg_content_type\": \"text\",\"msg_content\": \"Hello World\"}</code></pre><h4>📥 服务端响应格式</h4><pre><code class=\"language-json\">{\"code\": 0,\"message\": \"success\",\"data\": {\"msg_id\": \"msg_001\",\"msg_status\": 2,\"send_time\": \"2025-01-20 10:30:00\",\"sender_id\": \"user_456\",\"sender_nick\": \"张三\",\"conv_id\": \"user_123-user_456\"}}</code></pre><h4>🔄 消息类型说明</h4><ul><li><strong>chat</strong>：普通聊天消息</li><li><strong>chat_notice</strong>：聊天通知消息（如：用户加入群聊、退出群聊等）</li><li><strong>system</strong>：系统消息（如：系统维护通知、版本更新等）</li><li><strong>ping</strong>：心跳消息（保持连接活跃）</li></ul><h4>📋 接收者类型说明</h4><table border=\"1\" style=\"border-collapse: collapse; width: 100%;\"><tr><th>类型值</th><th>说明</th><th>receiver_id格式</th><th>示例</th></tr><tr><td>1</td><td>单聊</td><td>对方用户ID</td><td>user_123</td></tr><tr><td>2</td><td>群聊</td><td>群组ID</td><td>group_456</td></tr></table><h4>⚠️ 重要注意事项</h4><ol><li><strong>连接保持：</strong>建议每30秒发送一次ping消息保持连接</li><li><strong>消息大小：</strong>单个消息内容大小限制为10MB</li><li><strong>连接超时：</strong>无活动连接将在5分钟后自动断开</li><li><strong>重连机制：</strong>连接断开后客户端应实现自动重连</li><li><strong>消息去重：</strong>使用msg_client_id进行客户端消息去重</li></ol><h4>🚀 JavaScript使用示例</h4><pre><code class=\"language-javascript\">const ws = new WebSocket('ws://localhost:8000/chat/ws-login', [], {headers: {'Authorization': 'AyjChat your_token_here'}});ws.onopen = function() {console.log('WebSocket连接已建立');ws.send(JSON.stringify({msg_type: 'chat',msg_client_id: 'client_' + Date.now(),msg_receiver_id: 'user_123',msg_receiver_type: 1,msg_content_fmt: 1,msg_content_type: 'text',msg_content: 'Hello, 这是一条测试消息！'}));};ws.onmessage = function(event) {const response = JSON.parse(event.data);console.log('收到消息:', response);if (response.code === 0) {console.log('消息发送成功:', response.data);} else {console.error('消息发送失败:', response.message);}};</code></pre>"`

	MsgType         string `json:"msg_type" dc:"ws 消息类型: chat 聊天消息, chat_notice 聊天通知消息, system 系统信息, ping 心跳信息" `
	MsgClientId     string `json:"msg_client_id" dc:"ws 消息发送端生成的消息id,后端不入库(前端自己判断使用)"`
	MsgReceiverId   string `json:"msg_receiver_id" dc:"ws 消息接收端id: 单聊(对方user_id),群聊(group_id)"`
	MsgReceiverType int    `json:"msg_receiver_type" dc:"ws 消息接收端类型: 1 单聊， 2 群聊"`

	MsgContentFmt  int         `json:"msg_content_fmt" dc:"ws 内容格式: 1 json, 2 protobuf"`
	MsgContentType string      `json:"msg_content_type" dc:"ws 内容类型: text，image，audio，video ,file 等(可扩展)"`
	MsgContent     interface{} `json:"msg_content" dc:"ws 消息内容: 可扩展"`
}

// WebSocket连接成功后的响应
type ChatWsLoginRes struct {
	g.Meta `mime:"application/json" example:"string" description:"ws 客户端接收的消息格式："`

	Code    int         `json:"code" dc:"0成功，其他失败"`
	Message string      `json:"message" dc:"提示消息"`
	Data    MessageInfo `json:"data"  dc:"客户端收到统一消息内容"`
}

// ==================== 测试接口（使用外部文档） ====================

// 聊天功能测试接口请求
type ChatTestApiReq struct {
	g.Meta `path:"/test-api" tags:"聊天测试" method:"post" summary:"2、聊天功能测试接口" description:"<h3>🧪 聊天功能测试接口</h3><p>此接口用于测试聊天系统的各项功能，包括消息发送、接收、状态更新等。</p><h4>🎯 测试场景</h4><ul><li><strong>单聊测试：</strong>测试两个用户之间的消息收发</li><li><strong>群聊测试：</strong>测试群组内多用户消息广播</li><li><strong>消息状态测试：</strong>测试消息的发送、送达、已读状态</li><li><strong>离线消息测试：</strong>测试用户离线时的消息存储和推送</li><li><strong>消息类型测试：</strong>测试文本、图片、语音、视频等不同类型消息</li></ul><h4>📊 测试数据格式</h4><pre><code class=\"language-json\">{\"test_type\": \"single_chat\",\"sender_id\": \"test_user_001\",\"receiver_id\": \"test_user_002\",\"message_count\": 10,\"message_interval\": 1000,\"content_types\": [\"text\", \"image\", \"audio\"],\"auto_read\": true,\"simulate_offline\": false}</code></pre><h4>📈 测试结果统计</h4><table border=\"1\" style=\"border-collapse: collapse; width: 100%;\"><tr><th>指标</th><th>说明</th><th>期望值</th></tr><tr><td>发送成功率</td><td>成功发送的消息数量/总消息数量</td><td>&gt;= 99%</td></tr><tr><td>平均响应时间</td><td>从发送到收到确认的平均时间</td><td>&lt;= 100ms</td></tr><tr><td>消息丢失率</td><td>丢失的消息数量/总消息数量</td><td>&lt;= 0.1%</td></tr><tr><td>状态同步准确率</td><td>状态正确同步的消息数量/总消息数量</td><td>&gt;= 99.9%</td></tr></table><h4>⚡ 性能测试参数</h4><ul><li><strong>并发用户数：</strong>支持1000+并发连接</li><li><strong>消息吞吐量：</strong>每秒处理10000+条消息</li><li><strong>内存使用：</strong>单连接内存占用&lt;1MB</li><li><strong>CPU使用率：</strong>正常负载下&lt;50%</li></ul><h4>🛠️ 测试工具推荐</h4><ul><li><strong>Postman：</strong>支持WebSocket测试，可保存测试用例</li><li><strong>wscat：</strong>命令行WebSocket客户端，适合自动化测试</li><li><strong>Artillery：</strong>专业的WebSocket压力测试工具</li><li><strong>自定义脚本：</strong>使用Python/Node.js编写专门的测试脚本</li></ul>"`

	TestType        string   `json:"test_type" dc:"测试类型: single_chat 单聊测试, group_chat 群聊测试, message_status 消息状态测试, offline_message 离线消息测试, performance 性能测试" v:"required|in:single_chat,group_chat,message_status,offline_message,performance"`
	SenderId        string   `json:"sender_id" dc:"发送者用户ID" v:"required"`
	ReceiverId      string   `json:"receiver_id" dc:"接收者ID（用户ID或群组ID）" v:"required"`
	MessageCount    int      `json:"message_count" dc:"测试消息数量，默认10条" d:"10"`
	MessageInterval int      `json:"message_interval" dc:"消息发送间隔（毫秒），默认1000ms" d:"1000"`
	ContentTypes    []string `json:"content_types" dc:"消息内容类型列表: text, image, audio, video, file" d:"[\"text\"]"`
	AutoRead        bool     `json:"auto_read" dc:"是否自动标记为已读" d:"true"`
	SimulateOffline bool     `json:"simulate_offline" dc:"是否模拟离线场景" d:"false"`
}

// 聊天功能测试接口响应
type ChatTestApiRes struct {
	g.Meta `mime:"application/json"`

	Code    int                `json:"code" dc:"0成功，其他失败"`
	Message string             `json:"message" dc:"提示消息"`
	Data    ChatTestResultData `json:"data" dc:"测试结果数据"`
}

// 测试结果数据结构
type ChatTestResultData struct {
	TestId          string                 `json:"test_id" dc:"测试ID"`
	TestType        string                 `json:"test_type" dc:"测试类型"`
	StartTime       string                 `json:"start_time" dc:"测试开始时间"`
	EndTime         string                 `json:"end_time" dc:"测试结束时间"`
	Duration        int64                  `json:"duration" dc:"测试持续时间（毫秒）"`
	TotalMessages   int                    `json:"total_messages" dc:"总消息数量"`
	SuccessMessages int                    `json:"success_messages" dc:"成功发送的消息数量"`
	FailedMessages  int                    `json:"failed_messages" dc:"发送失败的消息数量"`
	SuccessRate     float64                `json:"success_rate" dc:"发送成功率（百分比）"`
	AvgResponseTime float64                `json:"avg_response_time" dc:"平均响应时间（毫秒）"`
	MaxResponseTime int64                  `json:"max_response_time" dc:"最大响应时间（毫秒）"`
	MinResponseTime int64                  `json:"min_response_time" dc:"最小响应时间（毫秒）"`
	MessageLossRate float64                `json:"message_loss_rate" dc:"消息丢失率（百分比）"`
	StatusSyncRate  float64                `json:"status_sync_rate" dc:"状态同步准确率（百分比）"`
	ErrorDetails    []ChatTestErrorDetail  `json:"error_details" dc:"错误详情列表"`
	PerformanceData ChatTestPerformanceData `json:"performance_data" dc:"性能数据"`
}

// 测试错误详情
type ChatTestErrorDetail struct {
	MessageId   string `json:"message_id" dc:"消息ID"`
	ErrorCode   int    `json:"error_code" dc:"错误码"`
	ErrorMsg    string `json:"error_msg" dc:"错误信息"`
	Timestamp   string `json:"timestamp" dc:"错误发生时间"`
	RetryCount  int    `json:"retry_count" dc:"重试次数"`
	IsResolved  bool   `json:"is_resolved" dc:"是否已解决"`
}

// 测试性能数据
type ChatTestPerformanceData struct {
	ConcurrentUsers    int     `json:"concurrent_users" dc:"并发用户数"`
	MessageThroughput  int     `json:"message_throughput" dc:"消息吞吐量（条/秒）"`
	MemoryUsage        float64 `json:"memory_usage" dc:"内存使用量（MB）"`
	CpuUsage           float64 `json:"cpu_usage" dc:"CPU使用率（百分比）"`
	NetworkLatency     float64 `json:"network_latency" dc:"网络延迟（毫秒）"`
	ConnectionCount    int     `json:"connection_count" dc:"活跃连接数"`
	DatabaseQueryTime  float64 `json:"database_query_time" dc:"数据库查询平均时间（毫秒）"`
	CacheHitRate       float64 `json:"cache_hit_rate" dc:"缓存命中率（百分比）"`
}

// ==================== 消息状态查询接口（使用外部文档） ====================

// 消息状态查询接口请求
type MessageStatusQueryReq struct {
	g.Meta `path:"/message-status" tags:"消息状态" method:"post" summary:"3、消息状态查询接口" description:"<h3>📊 消息状态查询接口</h3><p>此接口用于查询指定消息的详细状态信息，包括发送状态、接收状态、已读状态等。</p><h4>🎯 接口功能</h4><ul><li><strong>消息状态查询：</strong>查询单条或多条消息的状态</li><li><strong>批量状态查询：</strong>支持一次查询多条消息的状态</li><li><strong>状态历史追踪：</strong>查看消息状态的变更历史</li><li><strong>接收者状态：</strong>查看群聊中每个接收者的状态</li></ul><h4>📋 请求参数说明</h4><h5>单条消息查询</h5><pre><code class=\"language-json\">{\"query_type\": \"single\",\"msg_id\": \"msg_123456789\",\"include_history\": true,\"include_receivers\": true}</code></pre><h5>批量消息查询</h5><pre><code class=\"language-json\">{\"query_type\": \"batch\",\"msg_ids\": [\"msg_001\", \"msg_002\", \"msg_003\"],\"include_history\": false,\"include_receivers\": true}</code></pre><h4>📊 状态码说明</h4><table border=\"1\" style=\"border-collapse: collapse; width: 100%;\"><tr><th>状态码</th><th>状态名称</th><th>说明</th><th>适用场景</th></tr><tr><td>1</td><td>发送中</td><td>消息正在发送过程中</td><td>所有消息类型</td></tr><tr><td>2</td><td>已发送</td><td>消息已成功发送到服务器</td><td>所有消息类型</td></tr><tr><td>3</td><td>已送达</td><td>消息已送达到接收者设备</td><td>单聊、群聊</td></tr><tr><td>4</td><td>已读</td><td>消息已被接收者阅读</td><td>单聊、群聊</td></tr></table><h4>⚠️ 注意事项</h4><h5>权限控制</h5><ol><li><strong>发送者权限：</strong>只能查询自己发送的消息状态</li><li><strong>接收者权限：</strong>只能查询发送给自己的消息状态</li><li><strong>群聊权限：</strong>群成员可以查询群内消息状态</li></ol><h5>查询限制</h5><ol><li><strong>频率限制：</strong>每分钟最多查询100次</li><li><strong>数量限制：</strong>批量查询最多支持50条消息</li><li><strong>时间限制：</strong>历史查询最多支持30天内的数据</li></ol>"`

	QueryType        string   `json:"query_type" dc:"查询类型: single 单条查询, batch 批量查询, conversation 会话查询" v:"required|in:single,batch,conversation"`
	MsgId            string   `json:"msg_id" dc:"消息ID（单条查询时必填）"`
	MsgIds           []string `json:"msg_ids" dc:"消息ID列表（批量查询时必填）"`
	ConvId           string   `json:"conv_id" dc:"会话ID（会话查询时必填）"`
	StartTime        string   `json:"start_time" dc:"开始时间（会话查询时使用）"`
	EndTime          string   `json:"end_time" dc:"结束时间（会话查询时使用）"`
	IncludeHistory   bool     `json:"include_history" dc:"是否包含状态历史" d:"false"`
	IncludeReceivers bool     `json:"include_receivers" dc:"是否包含接收者详情" d:"true"`
	Limit            int      `json:"limit" dc:"查询数量限制（会话查询时使用）" d:"100"`
}

// 消息状态查询接口响应
type MessageStatusQueryRes struct {
	g.Meta `mime:"application/json"`

	Code    int                    `json:"code" dc:"0成功，其他失败"`
	Message string                 `json:"message" dc:"提示消息"`
	Data    MessageStatusQueryData `json:"data" dc:"查询结果数据"`
}

// 消息状态查询结果数据
type MessageStatusQueryData struct {
	QueryType    string              `json:"query_type" dc:"查询类型"`
	TotalCount   int                 `json:"total_count" dc:"总数量"`
	Messages     []MessageStatusInfo `json:"messages" dc:"消息状态信息列表"`
	QueryTime    string              `json:"query_time" dc:"查询时间"`
	ResponseTime int64               `json:"response_time" dc:"响应时间（毫秒）"`
}

// 消息状态信息
type MessageStatusInfo struct {
	MsgId          string                    `json:"msg_id" dc:"消息ID"`
	MsgType        string                    `json:"msg_type" dc:"消息类型"`
	SenderId       string                    `json:"sender_id" dc:"发送者ID"`
	ReceiverId     string                    `json:"receiver_id" dc:"接收者ID"`
	ReceiverType   int                       `json:"receiver_type" dc:"接收者类型"`
	SendTime       string                    `json:"send_time" dc:"发送时间"`
	CurrentStatus  MessageCurrentStatus      `json:"current_status" dc:"当前状态"`
	StatusHistory  []MessageStatusHistory    `json:"status_history" dc:"状态历史"`
	Receivers      []MessageReceiverStatus   `json:"receivers" dc:"接收者状态列表"`
}

// 消息当前状态
type MessageCurrentStatus struct {
	StatusCode int    `json:"status_code" dc:"状态码"`
	StatusName string `json:"status_name" dc:"状态名称"`
	UpdateTime string `json:"update_time" dc:"更新时间"`
}

// 消息状态历史
type MessageStatusHistory struct {
	StatusCode int    `json:"status_code" dc:"状态码"`
	StatusName string `json:"status_name" dc:"状态名称"`
	Timestamp  string `json:"timestamp" dc:"时间戳"`
}

// 消息接收者状态
type MessageReceiverStatus struct {
	ReceiverId     string `json:"receiver_id" dc:"接收者ID"`
	ReceiverNick   string `json:"receiver_nick" dc:"接收者昵称"`
	StatusCode     int    `json:"status_code" dc:"状态码"`
	StatusName     string `json:"status_name" dc:"状态名称"`
	ReadTime       string `json:"read_time" dc:"已读时间"`
	DeliveredTime  string `json:"delivered_time" dc:"送达时间"`
}
