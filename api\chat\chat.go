/*
******		FileName	:	chat.go
******		Describe	:	此文件主要用于聊天 websocket的接口
******		Date		:	2025-05-08
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   聊天 websocket
 */

package chat

import "github.com/gogf/gf/v2/frame/g"

// 在文件顶部定义文档常量
const (
	ChatWsLoginDescription = `
WebSocket聊天长连接登录接口详细说明：

<h3>🔗 连接方式</h3>
<p><strong>WebSocket URL：</strong><code>ws://{host}:{port}/chat/ws-login</code></p>
<p><strong>认证方式：</strong>请求头携带 <code>Authorization: AyjChat &lt;token&gt;</code></p>

<h3>📤 客户端发送消息格式</h3>
<pre><code>{
  "msg_type": "chat",
  "msg_client_id": "client_msg_001", 
  "msg_receiver_id": "user_123",
  "msg_receiver_type": 1,
  "msg_content_fmt": 1,
  "msg_content_type": "text",
  "msg_content": "Hello World"
}</code></pre>

<h3>📥 服务端响应格式</h3>
<pre><code>{
  "code": 0,
  "message": "success",
  "data": {
    "msg_id": "msg_001",
    "msg_status": 2,
    "send_time": "2025-01-20 10:30:00"
  }
}</code></pre>

<h3>🔄 消息类型说明</h3>
<ul>
  <li><strong>chat</strong>：普通聊天消息</li>
  <li><strong>chat_notice</strong>：聊天通知消息</li>
  <li><strong>system</strong>：系统消息</li>
  <li><strong>ping</strong>：心跳消息</li>
</ul>

<h3>⚠️ 注意事项</h3>
<ol>
  <li>连接建立后需要定期发送心跳消息保持连接</li>
  <li>消息发送失败时会返回相应的错误码</li>
  <li>单个消息内容大小限制为10MB</li>
  <li>连接超时时间为5分钟</li>
</ol>

<h3>🚀 使用示例</h3>
<h4>JavaScript示例：</h4>
<pre><code>const ws = new WebSocket('ws://localhost:8000/chat/ws-login', [], {
  headers: {
    'Authorization': 'AyjChat your_token_here'
  }
});

ws.onopen = function() {
  console.log('WebSocket连接已建立');
  
  // 发送聊天消息
  ws.send(JSON.stringify({
    msg_type: 'chat',
    msg_client_id: 'client_' + Date.now(),
    msg_receiver_id: 'user_123',
    msg_receiver_type: 1,
    msg_content_fmt: 1,
    msg_content_type: 'text',
    msg_content: 'Hello, this is a test message!'
  }));
};

ws.onmessage = function(event) {
  const response = JSON.parse(event.data);
  console.log('收到消息:', response);
};</code></pre>

<h4>Python示例：</h4>
<pre><code>import websocket
import json

def on_message(ws, message):
    response = json.loads(message)
    print(f"收到消息: {response}")

def on_open(ws):
    print("WebSocket连接已建立")
    
    # 发送聊天消息
    message = {
        "msg_type": "chat",
        "msg_client_id": f"client_{int(time.time())}",
        "msg_receiver_id": "user_123",
        "msg_receiver_type": 1,
        "msg_content_fmt": 1,
        "msg_content_type": "text",
        "msg_content": "Hello from Python!"
    }
    ws.send(json.dumps(message))

ws = websocket.WebSocketApp(
    "ws://localhost:8000/chat/ws-login",
    header={"Authorization": "AyjChat your_token_here"},
    on_open=on_open,
    on_message=on_message
)
ws.run_forever()</code></pre>
`
)

// 聊天websocket长连接 登录请求 (ws 消息结构体)
type ChatWsLoginReq struct {
	g.Meta `path:"/ws-login" tags:"聊天 WebSocket" method:"get" summary:"1、聊天长连接登录" description:"使用方式:ws://{host}:{port}/chat/ws-login，并在请求头中携带Authorization: AyjChat <token>格式的认证信息。连接成功后，客户端可以发送和接收实时消息。<br><br>ws 客户端发送的消息格式："`

	MsgType         string `json:"msg_type" dc:"ws 消息类型: chat 聊天消息,, chat_notice 聊天通知消息, system 系统信息, ping 心跳信息" `
	MsgClientId     string `json:"msg_client_id" dc:"ws 消息发送端, 生成的消息id,后端不入库(前端自己判断使用)"`
	MsgReceiverId   string `json:"msg_receiver_id" dc:"ws 消息接收端id: 单聊(对方user_id),群聊(group_id)"`
	MsgReceiverType int    `json:"msg_receiver_type" dc:"ws 消息接收端类型: 1 单聊， 2 群聊"`

	MsgContentFmt  int         `json:"msg_content_fmt" dc:"ws 内容格式: 1 json, 2 protobuf"`
	MsgContentType string      `json:"msg_content_type" dc:"ws 内容类型: ，text，image，audio，video ,file 等(可扩展)"`
	MsgContent     interface{} `json:"msg_content" dc:" ws 消息内容: 可扩展"`
}

// WebSocket连接成功后的响应
type ChatWsLoginRes struct {
	g.Meta `mime:"application/json" example:"string" description:"ws 客户端接收的消息格式："`

	Code    int         `json:"code" dc:"0成功，其他失败"`
	Message string      `json:"message" dc:"提示消息"`
	Data    MessageInfo `json:"data"  dc:"客户端收到统一消息内容"`
}
