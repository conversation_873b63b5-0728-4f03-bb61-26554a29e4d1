/*
******		FileName	:	chat.go
******		Describe	:	此文件主要用于聊天 websocket的接口
******		Date		:	2025-05-08
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   聊天 websocket
 */

package chat

import "github.com/gogf/gf/v2/frame/g"

// 聊天websocket长连接 登录请求 (ws 消息结构体)
type ChatWsLoginReq struct {
	g.Meta `path:"/ws-login" tags:"聊天 WebSocket" method:"get" summary:"1、聊天长连接登录" description:"使用方式:ws://{host}:{port}/chat/ws-login，并在请求头中携带Authorization: AyjChat <token>格式的认证信息。连接成功后，客户端可以发送和接收实时消息。<br><br>ws 客户端发送的消息格式："`

	MsgType         string `json:"msg_type" dc:"ws 消息类型: chat 聊天消息,, chat_notice 聊天通知消息, system 系统信息, ping 心跳信息" `
	MsgClientId     string `json:"msg_client_id" dc:"ws 消息发送端, 生成的消息id,后端不入库(前端自己判断使用)"`
	MsgReceiverId   string `json:"msg_receiver_id" dc:"ws 消息接收端id: 单聊(对方user_id),群聊(group_id)"`
	MsgReceiverType int    `json:"msg_receiver_type" dc:"ws 消息接收端类型: 1 单聊， 2 群聊"`

	MsgContentFmt  int         `json:"msg_content_fmt" dc:"ws 内容格式: 1 json, 2 protobuf"`
	MsgContentType string      `json:"msg_content_type" dc:"ws 内容类型: ，text，image，audio，video ,file 等(可扩展)"`
	MsgContent     interface{} `json:"msg_content" dc:" ws 消息内容: 可扩展"`
}

// WebSocket连接成功后的响应
type ChatWsLoginRes struct {
	g.Meta `mime:"application/json" example:"string" description:"ws 客户端接收的消息格式："`

	Code    int         `json:"code" dc:"0成功，其他失败"`
	Message string      `json:"message" dc:"提示消息"`
	Data    MessageInfo `json:"data"  dc:"客户端收到统一消息内容"`
}
