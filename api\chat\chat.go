/*
******		FileName	:	chat.go
******		Describe	:	此文件主要用于聊天 websocket的接口
******		Date		:	2025-05-08
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   聊天 websocket
 */

package chat

import "github.com/gogf/gf/v2/frame/g"

// ==================== 外部文档引用功能 ====================
//
// 使用方法：
// 1. 在 docs/ 目录下创建 .md 文件
// 2. 使用 GetDocContent("filename.md") 获取转换后的HTML内容
// 3. 在接口定义中引用文档内容
//
// 优势：
// - 文档内容独立维护，支持Markdown格式
// - 代码更简洁，可读性更好
// - 支持版本控制和团队协作
// - 自动转换Markdown到HTML

// 文档内容常量（编译时生成）
var (
	// WebSocket登录接口文档
	WsLoginDoc = GetDocContent("ws-login.md")

	// 测试接口文档
	TestApiDoc = GetDocContent("test-api.md")

	// 消息状态查询接口文档
	MessageStatusDoc = GetDocContent("message-status.md")
)

// 聊天websocket长连接 登录请求 (ws 消息结构体)
type ChatWsLoginReq struct {
	g.Meta `path:"/ws-login" tags:"聊天 WebSocket" method:"get" summary:"1、聊天长连接登录"`

	MsgType         string `json:"msg_type" dc:"ws 消息类型: chat 聊天消息, chat_notice 聊天通知消息, system 系统信息, ping 心跳信息" `
	MsgClientId     string `json:"msg_client_id" dc:"ws 消息发送端生成的消息id,后端不入库(前端自己判断使用)"`
	MsgReceiverId   string `json:"msg_receiver_id" dc:"ws 消息接收端id: 单聊(对方user_id),群聊(group_id)"`
	MsgReceiverType int    `json:"msg_receiver_type" dc:"ws 消息接收端类型: 1 单聊， 2 群聊"`

	MsgContentFmt  int         `json:"msg_content_fmt" dc:"ws 内容格式: 1 json, 2 protobuf"`
	MsgContentType string      `json:"msg_content_type" dc:"ws 内容类型: text，image，audio，video ,file 等(可扩展)"`
	MsgContent     interface{} `json:"msg_content" dc:"ws 消息内容: 可扩展"`
}

// WebSocket连接成功后的响应
type ChatWsLoginRes struct {
	g.Meta `mime:"application/json" example:"string" description:"ws 客户端接收的消息格式："`

	Code    int         `json:"code" dc:"0成功，其他失败"`
	Message string      `json:"message" dc:"提示消息"`
	Data    MessageInfo `json:"data"  dc:"客户端收到统一消息内容"`
}

// ==================== 测试接口（使用外部文档） ====================

// 聊天功能测试接口请求
type ChatTestApiReq struct {
	g.Meta `path:"/test-api" tags:"聊天测试" method:"post" summary:"2、聊天功能测试接口"`

	TestType        string   `json:"test_type" dc:"测试类型: single_chat 单聊测试, group_chat 群聊测试, message_status 消息状态测试, offline_message 离线消息测试, performance 性能测试" v:"required|in:single_chat,group_chat,message_status,offline_message,performance"`
	SenderId        string   `json:"sender_id" dc:"发送者用户ID" v:"required"`
	ReceiverId      string   `json:"receiver_id" dc:"接收者ID（用户ID或群组ID）" v:"required"`
	MessageCount    int      `json:"message_count" dc:"测试消息数量，默认10条" d:"10"`
	MessageInterval int      `json:"message_interval" dc:"消息发送间隔（毫秒），默认1000ms" d:"1000"`
	ContentTypes    []string `json:"content_types" dc:"消息内容类型列表: text, image, audio, video, file" d:"[\"text\"]"`
	AutoRead        bool     `json:"auto_read" dc:"是否自动标记为已读" d:"true"`
	SimulateOffline bool     `json:"simulate_offline" dc:"是否模拟离线场景" d:"false"`
}

// 聊天功能测试接口响应
type ChatTestApiRes struct {
	g.Meta `mime:"application/json"`

	Code    int                `json:"code" dc:"0成功，其他失败"`
	Message string             `json:"message" dc:"提示消息"`
	Data    ChatTestResultData `json:"data" dc:"测试结果数据"`
}

// 测试结果数据结构
type ChatTestResultData struct {
	TestId          string                 `json:"test_id" dc:"测试ID"`
	TestType        string                 `json:"test_type" dc:"测试类型"`
	StartTime       string                 `json:"start_time" dc:"测试开始时间"`
	EndTime         string                 `json:"end_time" dc:"测试结束时间"`
	Duration        int64                  `json:"duration" dc:"测试持续时间（毫秒）"`
	TotalMessages   int                    `json:"total_messages" dc:"总消息数量"`
	SuccessMessages int                    `json:"success_messages" dc:"成功发送的消息数量"`
	FailedMessages  int                    `json:"failed_messages" dc:"发送失败的消息数量"`
	SuccessRate     float64                `json:"success_rate" dc:"发送成功率（百分比）"`
	AvgResponseTime float64                `json:"avg_response_time" dc:"平均响应时间（毫秒）"`
	MaxResponseTime int64                  `json:"max_response_time" dc:"最大响应时间（毫秒）"`
	MinResponseTime int64                  `json:"min_response_time" dc:"最小响应时间（毫秒）"`
	MessageLossRate float64                `json:"message_loss_rate" dc:"消息丢失率（百分比）"`
	StatusSyncRate  float64                `json:"status_sync_rate" dc:"状态同步准确率（百分比）"`
	ErrorDetails    []ChatTestErrorDetail  `json:"error_details" dc:"错误详情列表"`
	PerformanceData ChatTestPerformanceData `json:"performance_data" dc:"性能数据"`
}

// 测试错误详情
type ChatTestErrorDetail struct {
	MessageId   string `json:"message_id" dc:"消息ID"`
	ErrorCode   int    `json:"error_code" dc:"错误码"`
	ErrorMsg    string `json:"error_msg" dc:"错误信息"`
	Timestamp   string `json:"timestamp" dc:"错误发生时间"`
	RetryCount  int    `json:"retry_count" dc:"重试次数"`
	IsResolved  bool   `json:"is_resolved" dc:"是否已解决"`
}

// 测试性能数据
type ChatTestPerformanceData struct {
	ConcurrentUsers    int     `json:"concurrent_users" dc:"并发用户数"`
	MessageThroughput  int     `json:"message_throughput" dc:"消息吞吐量（条/秒）"`
	MemoryUsage        float64 `json:"memory_usage" dc:"内存使用量（MB）"`
	CpuUsage           float64 `json:"cpu_usage" dc:"CPU使用率（百分比）"`
	NetworkLatency     float64 `json:"network_latency" dc:"网络延迟（毫秒）"`
	ConnectionCount    int     `json:"connection_count" dc:"活跃连接数"`
	DatabaseQueryTime  float64 `json:"database_query_time" dc:"数据库查询平均时间（毫秒）"`
	CacheHitRate       float64 `json:"cache_hit_rate" dc:"缓存命中率（百分比）"`
}

// ==================== 消息状态查询接口（使用外部文档） ====================

// 消息状态查询接口请求
type MessageStatusQueryReq struct {
	g.Meta `path:"/message-status" tags:"消息状态" method:"post" summary:"3、消息状态查询接口"`

	QueryType        string   `json:"query_type" dc:"查询类型: single 单条查询, batch 批量查询, conversation 会话查询" v:"required|in:single,batch,conversation"`
	MsgId            string   `json:"msg_id" dc:"消息ID（单条查询时必填）"`
	MsgIds           []string `json:"msg_ids" dc:"消息ID列表（批量查询时必填）"`
	ConvId           string   `json:"conv_id" dc:"会话ID（会话查询时必填）"`
	StartTime        string   `json:"start_time" dc:"开始时间（会话查询时使用）"`
	EndTime          string   `json:"end_time" dc:"结束时间（会话查询时使用）"`
	IncludeHistory   bool     `json:"include_history" dc:"是否包含状态历史" d:"false"`
	IncludeReceivers bool     `json:"include_receivers" dc:"是否包含接收者详情" d:"true"`
	Limit            int      `json:"limit" dc:"查询数量限制（会话查询时使用）" d:"100"`
}

// 消息状态查询接口响应
type MessageStatusQueryRes struct {
	g.Meta `mime:"application/json"`

	Code    int                    `json:"code" dc:"0成功，其他失败"`
	Message string                 `json:"message" dc:"提示消息"`
	Data    MessageStatusQueryData `json:"data" dc:"查询结果数据"`
}

// 消息状态查询结果数据
type MessageStatusQueryData struct {
	QueryType    string              `json:"query_type" dc:"查询类型"`
	TotalCount   int                 `json:"total_count" dc:"总数量"`
	Messages     []MessageStatusInfo `json:"messages" dc:"消息状态信息列表"`
	QueryTime    string              `json:"query_time" dc:"查询时间"`
	ResponseTime int64               `json:"response_time" dc:"响应时间（毫秒）"`
}

// 消息状态信息
type MessageStatusInfo struct {
	MsgId          string                    `json:"msg_id" dc:"消息ID"`
	MsgType        string                    `json:"msg_type" dc:"消息类型"`
	SenderId       string                    `json:"sender_id" dc:"发送者ID"`
	ReceiverId     string                    `json:"receiver_id" dc:"接收者ID"`
	ReceiverType   int                       `json:"receiver_type" dc:"接收者类型"`
	SendTime       string                    `json:"send_time" dc:"发送时间"`
	CurrentStatus  MessageCurrentStatus      `json:"current_status" dc:"当前状态"`
	StatusHistory  []MessageStatusHistory    `json:"status_history" dc:"状态历史"`
	Receivers      []MessageReceiverStatus   `json:"receivers" dc:"接收者状态列表"`
}

// 消息当前状态
type MessageCurrentStatus struct {
	StatusCode int    `json:"status_code" dc:"状态码"`
	StatusName string `json:"status_name" dc:"状态名称"`
	UpdateTime string `json:"update_time" dc:"更新时间"`
}

// 消息状态历史
type MessageStatusHistory struct {
	StatusCode int    `json:"status_code" dc:"状态码"`
	StatusName string `json:"status_name" dc:"状态名称"`
	Timestamp  string `json:"timestamp" dc:"时间戳"`
}

// 消息接收者状态
type MessageReceiverStatus struct {
	ReceiverId     string `json:"receiver_id" dc:"接收者ID"`
	ReceiverNick   string `json:"receiver_nick" dc:"接收者昵称"`
	StatusCode     int    `json:"status_code" dc:"状态码"`
	StatusName     string `json:"status_name" dc:"状态名称"`
	ReadTime       string `json:"read_time" dc:"已读时间"`
	DeliveredTime  string `json:"delivered_time" dc:"送达时间"`
}
