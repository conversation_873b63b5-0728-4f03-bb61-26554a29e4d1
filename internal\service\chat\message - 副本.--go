/*
******		FileName	:	message.go
******		Describe	:	此文件主要用于聊天消息数据的管理
******		Date		:	2025-05-10
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   聊天 数据保存与获取的实现
 */

package server

import (
	"ayj_chat_back/api/chat"
	"ayj_chat_back/internal/consts"
	"ayj_chat_back/internal/dao"
	"ayj_chat_back/internal/model/chat"
	"ayj_chat_back/internal/public/tools"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"gorm.io/gorm"
	"strings"
	"time"
)

// ServerMessage 消息服务结构体
type ServerMessage struct {
	// 分表策略
	shardCount int // 分表数量
}

// NewServerMessage 创建消息服务
func NewServerMessage() *ServerMessage {
	return &ServerMessage{
		shardCount: 10, // 设置分表数量，可以根据实际需求调整
	}
}

// 更新消息体状态
func (s *ServerMessage) updateMessageStatus(msgId string, receiverId string, status int) {
	// 更新消息接收状态
	dao.Db.Model(&modelChat.ChatMessageReceiver{}).Where(
		"msg_id = ? AND receiver_id = ?",
		msgId, receiverId,
	).Update("status", status)
}

// 计算分片键 - 用于分表策略
func (s *ServerMessage) calculateShardKey(senderId string, receiverId string) int {
	// 使用接收者ID的哈希值作为分片键
	// 如果是群聊，直接使用群ID
	// 如果是单聊，使用两个用户ID的组合
	var key string
	if receiverId != "" {
		key = receiverId
	} else {
		key = senderId
	}

	// 简单的哈希算法，将字符串转为整数后对分片数取模
	hashValue := 0
	for _, c := range key {
		hashValue = hashValue*31 + int(c)
	}

	// 确保返回正数
	if hashValue < 0 {
		hashValue = -hashValue
	}

	return hashValue % s.shardCount
}

// 生成会话ID(单聊 与 群聊, 客户端也与客户端生成的会话id 一致)
func (s *ServerMessage) generateConvId(convType int, userId string, targetId string) string {
	if convType == consts.ChatMsgType_Private {
		// 单聊：确保两个用户ID按字典序排序，保证生成的会话ID唯一
		if userId < targetId {
			return userId + "-" + targetId
		} else {
			return targetId + "-" + userId
		}
	} else {
		// 群聊：直接使用群ID作为会话ID
		return targetId
	}
}

// 保存单聊消息到数据库
func (s *ServerMessage) savePrivateMessageToDatabase(msg *MessageItem) {
	// 计算分片键
	shardKey := s.calculateShardKey(msg.SenderId, msg.ReceiverId)

	// 创建消息记录
	now := time.Now()
	sendTime := msg.SendTime
	if sendTime == nil {
		sendTime = gtime.Now()
	}

	// 将消息内容转换为字符串
	var msgContentStr string
	if contentBytes, err := json.Marshal(msg.MsgContent); err == nil {
		msgContentStr = string(contentBytes)
	} else {
		msgContentStr = fmt.Sprintf("%v", msg.MsgContent)
	}

	// 创建消息记录
	message := &modelChat.ChatMessage{
		MsgType:        msg.MsgType,
		MsgId:          msg.MsgId,
		Status:         consts.MessageStatus_Sended, // 已发送
		SenderId:       msg.SenderId,
		SendTime:       sendTime,
		ReceiverId:     msg.ReceiverId,
		ReceiverType:   msg.ReceiverType, // 1表示单聊
		MsgContentFmt:  msg.MsgContentFmt,
		MsgContentType: msg.MsgContentType,
		MsgContent:     msgContentStr,
		ShardKey:       shardKey,
		CreatedAt:      &now,
		UpdatedAt:      &now,
	}

	// 1、保存消息
	dao.Db.Create(message)

	// 创建消息接收记录
	receiver := &modelChat.ChatMessageReceiver{
		MsgId:      msg.MsgId,
		ReceiverId: msg.ReceiverId,
		Status:     message.Status, // 已发送
		ShardKey:   s.calculateShardKey("", msg.ReceiverId),
		CreatedAt:  &now,
		UpdatedAt:  &now,
	}

	// 2、保存接收记录
	dao.Db.Create(receiver)

	// 3、更新或创建会话
	s.updateConversation(consts.ChatMsgType_Private, msg.SenderId, msg.ReceiverId, msg.MsgId, sendTime)
	s.updateConversation(consts.ChatMsgType_Private, msg.ReceiverId, msg.SenderId, msg.MsgId, sendTime)
}

// 保存群聊消息到数据库
func (s *ServerMessage) saveGroupMessageToDatabase(msg *MessageItem, members []string) {
	// 计算分片键
	shardKey := s.calculateShardKey(msg.SenderId, msg.ReceiverId)

	// 创建消息记录
	now := time.Now()
	sendTime := msg.SendTime
	if sendTime == nil {
		sendTime = gtime.Now()
	}

	// 将消息内容转换为字符串
	var msgContentStr string
	if contentBytes, err := json.Marshal(msg.MsgContent); err == nil {
		msgContentStr = string(contentBytes)
	} else {
		msgContentStr = fmt.Sprintf("%v", msg.MsgContent)
	}

	// 1、创建消息记录
	message := &modelChat.ChatMessage{
		MsgType:        msg.MsgType,
		MsgId:          msg.MsgId,
		Status:         consts.MessageStatus_Sended, // 已发送
		SenderId:       msg.SenderId,
		SendTime:       sendTime,
		ReceiverId:     msg.ReceiverId,   // 群聊的ReceiverId就是群ID
		ReceiverType:   msg.ReceiverType, // 2表示群聊
		MsgContentFmt:  msg.MsgContentFmt,
		MsgContentType: msg.MsgContentType,
		MsgContent:     msgContentStr,
		ShardKey:       shardKey,
		CreatedAt:      &now,
		UpdatedAt:      &now,
	}

	// 保存消息
	dao.Db.Create(message)

	// 为每个成员创建消息接收记录
	for _, memberUserId := range members {
		// 跳过发送者自己
		/*if memberUserId == msg.SenderId {
			continue
		}*/

		receiver := &modelChat.ChatMessageReceiver{
			MsgId:      msg.MsgId,
			ReceiverId: memberUserId,
			Status:     message.Status, // 已发送
			ShardKey:   s.calculateShardKey("", memberUserId),
			CreatedAt:  &now,
			UpdatedAt:  &now,
		}

		// 1、保存接收记录
		dao.Db.Create(receiver)

		// 2、更新成员的会话(包含了自己与其他成员)
		s.updateConversation(consts.ChatMsgType_Group, memberUserId, msg.ReceiverId, msg.MsgId, sendTime)
	}
	// 更新发送者的会话
	//s.updateConversation(consts.ChatMsgType_Group, msg.SenderId, msg.ReceiverId, msg.MsgId, msgContentStr, sendTime)
}

// 更新会话
func (s *ServerMessage) updateConversation(ConvType int, sendUserId string, targetId string, msgId string, msgTime *gtime.Time) {
	// 生成会话ID
	convId := s.generateConvId(ConvType, sendUserId, targetId)

	// 查找现有会话
	var conversation modelChat.ChatConversation
	result := dao.Db.Model(&modelChat.ChatConversation{}).Where(
		"user_id = ? AND target_id = ? AND conv_type = ?",
		sendUserId, targetId, ConvType,
	).First(&conversation)

	now := time.Now()

	// 如果会话不存在，创建新会话
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// 创建新会话
			newConv := &modelChat.ChatConversation{
				ConvId:      convId,
				ConvType:    ConvType,
				UserId:      sendUserId,
				TargetId:    targetId,
				LastMsgId:   msgId,
				LastMsgTime: msgTime,
				UnreadCount: 0,
				Status:      consts.ChatConvStatus_Normal, // 正常
				CreatedAt:   &now,
				UpdatedAt:   &now,
			}

			// 如果是接收者，设置未读数为1
			if sendUserId != targetId {
				newConv.UnreadCount = 1
			}

			// 保存新会话
			result = dao.Db.Create(newConv)
			if result.Error != nil {
				g.Log().Errorf(context.Background(), "创建会话失败 :%v", result)
			}
		}
	} else {
		// 更新现有会话
		updateData := map[string]interface{}{
			"last_msg_id":   msgId,
			"last_msg_time": msgTime,
			"updated_at":    &now,
		}

		// 如果是接收者，增加未读数
		if sendUserId != conversation.UserId {
			updateData["unread_count"] = gorm.Expr("unread_count + 1")
		}

		// 更新会话
		dao.Db.Model(&conversation).Updates(updateData)
	}
}

// 获取用户基本信息
func (s *ServerMessage) getUserInfo(userId string) (userName string, userAvatar string) {
	// 这里应该调用用户服务获取用户信息
	// 为简化实现，这里直接从数据库查询
	var userInfo struct {
		UserName   string
		UserAvatar string
	}

	// 查询用户信息
	dao.Db.Table("user_info").Select("user_name, user_avatar").Where("user_id = ?", userId).Scan(&userInfo)

	return userInfo.UserName, userInfo.UserAvatar
}

// GetMsgConvList 1、会话列表-获取
func (s *ServerMessage) GetMsgConvList(req *chat.GetMsgConvListReq, userId string) (res *chat.GetMsgConvListRes, err error) {
	// 1. 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 || req.Size > 100 {
		req.Size = 100
	}

	// 2. 构建查询条件
	query := dao.Db.Model(&modelChat.ChatConversation{}).
		Where("user_id = ? AND deleted_at IS NULL", userId)

	// 根据会话类型筛选
	if req.ConvType > 0 {
		query = query.Where("conv_type = ?", req.ConvType)
	}

	// 3. 获取会话总数和总未读数（单次查询优化）
	type CountResult struct {
		TotalCount       int64
		TotalUnreadCount int64
	}
	var countResult CountResult

	// 构建SQL查询
	var sqlQuery string
	var sqlParams []interface{}

	if req.ConvType > 0 {
		// 如果指定了会话类型
		sqlQuery = `
			SELECT
				COUNT(*) as total_count,
				COALESCE(SUM(unread_count), 0) as total_unread_count
			FROM chat_conversations
			WHERE user_id = ? AND deleted_at IS NULL
			AND conv_type = ?`
		sqlParams = []interface{}{userId, req.ConvType}
	} else {
		// 如果没有指定会话类型
		sqlQuery = `
			SELECT
				COUNT(*) as total_count,
				COALESCE(SUM(unread_count), 0) as total_unread_count
			FROM chat_conversations
			WHERE user_id = ? AND deleted_at IS NULL`
		sqlParams = []interface{}{userId}
	}

	// 执行查询
	result := dao.Db.Raw(sqlQuery, sqlParams...).Scan(&countResult)

	if result.Error != nil {
		return nil, result.Error
	}

	// 4. 获取会话列表和最后一条消息内容
	type ConversationWithLastMsg struct {
		// 会话基本信息
		modelChat.ChatConversation
		// 最后一条消息信息
		LastMsgContentFmt  int    `gorm:"column:last_msg_content_fmt"`
		LastMsgContentType string `gorm:"column:last_msg_content_type"`
		LastMsgContent     string `gorm:"column:last_msg_content"`
	}

	var conversationsWithMsg []ConversationWithLastMsg

	// 使用JOIN查询获取会话和最后一条消息的详细信息
	queryBuilder := dao.Db.Table("chat_conversations as cc").
		Select(`
			cc.*,
			cm.msg_content_fmt as last_msg_content_fmt,
			cm.msg_content_type as last_msg_content_type,
			cm.msg_content as last_msg_content
		`).
		Joins("LEFT JOIN chat_messages as cm ON cc.last_msg_id = cm.msg_id").
		Where("cc.user_id = ? AND cc.deleted_at IS NULL", userId)

	// 根据会话类型筛选
	if req.ConvType > 0 {
		queryBuilder = queryBuilder.Where("cc.conv_type = ?", req.ConvType)
	}

	// 执行查询
	result = queryBuilder.Order("CASE WHEN cc.status = 2 THEN 1 ELSE 0 END DESC, cc.last_msg_time DESC"). // 置顶的排在前面，然后按最后消息时间倒序
														Offset((req.Page - 1) * req.Size).
														Limit(req.Size).
														Find(&conversationsWithMsg)

	if result.Error != nil {
		return nil, result.Error
	}

	// 5. 构建响应数据
	convList := make([]chat.MsgConvInfo, 0, len(conversationsWithMsg))

	for _, conv := range conversationsWithMsg {
		// 解析消息内容
		var lastMsgContent interface{}
		if conv.LastMsgContent != "" {
			if err := json.Unmarshal([]byte(conv.LastMsgContent), &lastMsgContent); err != nil {
				// 如果解析失败，使用原始字符串
				lastMsgContent = conv.LastMsgContent
			}
		}

		// 构建会话信息
		convInfo := chat.MsgConvInfo{
			ConvId:             conv.ConvId,
			ConvType:           conv.ConvType,
			TargetId:           conv.TargetId,
			UnreadCount:        conv.UnreadCount,
			Status:             conv.Status,
			LastMsgTime:        tools.GtimeToStringNMDHMS(conv.LastMsgTime),
			LastMsgContentFmt:  conv.LastMsgContentFmt,
			LastMsgContentType: conv.LastMsgContentType,
			LastMsgContent:     lastMsgContent,
		}

		convList = append(convList, convInfo)
	}

	// 6. 返回结果
	res = &chat.GetMsgConvListRes{
		List:             convList,
		TotalUnreadCount: int(countResult.TotalUnreadCount),
		HasMore:          int(countResult.TotalCount) > (req.Page * req.Size),
	}

	return res, nil
}

// GetMsgListForConvId 2、会话消息获取
func (s *ServerMessage) GetMsgListForConvId(req *chat.GetMsgListForConvIdReq, userId string) (res *chat.GetMsgListForConvIdRes, err error) {
	// 1. 参数校验
	if req.ConvId == "" {
		return nil, errors.New("会话ID不能为空")
	}

	// 2. 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 || req.Size > 100 {
		req.Size = 20 // 默认每页20条消息
	}

	// 3. 解析会话ID，确定会话类型和目标ID
	var convType int
	var targetId string

	if strings.Contains(req.ConvId, "-") {
		// 3.1 单聊会话
		convType = consts.ChatMsgType_Private
		parts := strings.Split(req.ConvId, "-")
		if len(parts) != 2 {
			return nil, errors.New("无效的会话ID格式")
		}

		// 确定目标ID（对方ID）
		if parts[0] == userId {
			targetId = parts[1]
		} else if parts[1] == userId {
			targetId = parts[0]
		} else {
			return nil, errors.New("您不是该会话的成员")
		}

		// 验证是否是好友关系
		var friendCount int64
		result := dao.Db.Model(&modelChat.FriendRelation{}).
			Where("user_id = ? AND friend_id = ? AND deleted_at IS NULL", userId, targetId).
			Count(&friendCount)

		if result.Error != nil {
			return nil, fmt.Errorf("验证好友关系失败: %w", result.Error)
		}

		if friendCount == 0 {
			return nil, errors.New("对方不是您的好友，无法查看消息")
		}
	} else {
		// 3.2 群聊会话
		convType = consts.ChatMsgType_Group
		targetId = req.ConvId

		// 检查用户是否是群成员
		var memberCount int64
		result := dao.Db.Model(&modelChat.ChatGroupMember{}).
			Where("group_id = ? AND user_id = ? AND deleted_at IS NULL AND exit_time IS NULL", targetId, userId).
			Count(&memberCount)

		if result.Error != nil {
			return nil, fmt.Errorf("验证群成员关系失败: %w", result.Error)
		}

		if memberCount == 0 {
			return nil, errors.New("您不是该群组的成员，无法查看消息")
		}
	}

	// 4. 构建查询 - 使用联表查询一次性获取所有需要的数据
	type MessageWithReceiver struct {
		// 消息基本信息
		MsgId          string      `gorm:"column:msg_id"`
		MsgType        string      `gorm:"column:msg_type"`
		Status         int         `gorm:"column:status"`
		SenderId       string      `gorm:"column:sender_id"`
		SendTime       *gtime.Time `gorm:"column:send_time"`
		MsgContentFmt  int         `gorm:"column:msg_content_fmt"`
		MsgContentType string      `gorm:"column:msg_content_type"`
		MsgContent     string      `gorm:"column:msg_content"`

		// 接收状态信息
		ReadTime *gtime.Time `gorm:"column:read_time"`
	}

	var messagesWithReceiver []MessageWithReceiver
	var queryBuilder *gorm.DB

	if convType == consts.ChatMsgType_Private {
		// 4.1 单聊：查询双方之间的消息
		queryBuilder = dao.Db.Table("chat_messages as cm").
			Select(`
				cm.msg_id, cm.msg_type, cm.status, cm.sender_id, cm.send_time,
				cm.msg_content_fmt, cm.msg_content_type, cm.msg_content,
				cmr.read_time
			`).
			Joins("LEFT JOIN chat_message_receivers as cmr ON cm.msg_id = cmr.msg_id AND cmr.receiver_id = ?", userId).
			Where("((cm.sender_id = ? AND cm.receiver_id = ?) OR (cm.sender_id = ? AND cm.receiver_id = ?)) AND cm.deleted_at IS NULL",
				userId, targetId, targetId, userId)
	} else {
		// 4.2 群聊：查询群内的消息
		queryBuilder = dao.Db.Table("chat_messages as cm").
			Select(`
				cm.msg_id, cm.msg_type, cm.status, cm.sender_id, cm.send_time,
				cm.msg_content_fmt, cm.msg_content_type, cm.msg_content,
				cmr.read_time
			`).
			Joins("LEFT JOIN chat_message_receivers as cmr ON cm.msg_id = cmr.msg_id AND cmr.receiver_id = ?", userId).
			Where("cm.receiver_id = ? AND cm.receiver_type = ? AND cm.deleted_at IS NULL",
				targetId, convType)
	}

	// 5. 获取消息总数
	var totalCount int64
	countQuery := queryBuilder.Session(&gorm.Session{})
	result := countQuery.Count(&totalCount)
	if result.Error != nil {
		return nil, fmt.Errorf("获取消息总数失败: %w", result.Error)
	}

	// 6. 获取消息列表
	result = queryBuilder.Order("cm.send_time DESC"). // 按发送时间降序
								Offset((req.Page - 1) * req.Size).
								Limit(req.Size).
								Find(&messagesWithReceiver)

	if result.Error != nil {
		return nil, fmt.Errorf("获取消息列表失败: %w", result.Error)
	}

	// 7. 收集所有发送者ID，用于批量获取用户信息
	senderIds := make([]string, 0, len(messagesWithReceiver))
	senderIdMap := make(map[string]bool)

	for _, msg := range messagesWithReceiver {
		if !senderIdMap[msg.SenderId] {
			senderIds = append(senderIds, msg.SenderId)
			senderIdMap[msg.SenderId] = true
		}
	}

	// 8. 批量获取用户信息
	type UserBasicInfo struct {
		UserId     string `gorm:"column:user_id"`
		UserName   string `gorm:"column:user_name"`
		UserAvatar string `gorm:"column:user_avatar"`
	}

	var userInfos []UserBasicInfo
	if len(senderIds) > 0 {
		dao.Db.Table("user_info").
			Select("user_id, user_name, user_avatar").
			Where("user_id IN ?", senderIds).
			Find(&userInfos)
	}

	// 创建用户信息映射
	userInfoMap := make(map[string]UserBasicInfo, len(userInfos))
	for _, user := range userInfos {
		userInfoMap[user.UserId] = user
	}

	// 9. 构建响应数据
	msgList := make([]chat.MessageInfo, 0, len(messagesWithReceiver))

	for _, msg := range messagesWithReceiver {
		// 解析消息内容
		var msgContent interface{}
		if msg.MsgContent != "" {
			if err := json.Unmarshal([]byte(msg.MsgContent), &msgContent); err != nil {
				// 如果解析失败，使用原始字符串
				msgContent = msg.MsgContent
			}
		}

		// 获取发送者信息
		userInfo, exists := userInfoMap[msg.SenderId]
		senderName := ""
		senderAvatar := ""
		if exists {
			senderName = userInfo.UserName
			senderAvatar = userInfo.UserAvatar
		}

		// 构建消息信息
		messageInfo := chat.MessageInfo{
			MsgType:        msg.MsgType,
			MsgId:          msg.MsgId,
			MsgStatus:      msg.Status,
			MsgReadTime:    tools.GtimeToStringNMDHMS(msg.ReadTime),
			MsgContentFmt:  msg.MsgContentFmt,
			MsgContentType: msg.MsgContentType,
			MsgContent:     msgContent,
			SendTime:       tools.GtimeToStringNMDHMS(msg.SendTime),
			SenderId:       msg.SenderId,
			SenderNick:     senderName,
			SenderAvatar:   senderAvatar,
		}

		msgList = append(msgList, messageInfo)
	}

	// 10. 异步更新会话未读数为0
	go func() {
		dao.Db.Model(&modelChat.ChatConversation{}).
			Where("user_id = ? AND target_id = ? AND conv_type = ?", userId, targetId, convType).
			Update("unread_count", 0)
	}()

	// 11. 返回结果
	res = &chat.GetMsgListForConvIdRes{
		List:    msgList,
		Count:   int(totalCount),
		HasMore: int(totalCount) > (req.Page * req.Size),
	}

	return res, nil
}

// MarkMsgRead 3、会话消息-标记已读(部分消息)
func (s *ServerMessage) MarkConvMsgRead(req *chat.MarkConvMsgReadReq, userId string) (res *chat.MarkConvMsgReadRes, err error) {
	// 参数校验
	if req.ConvId == "" {
		return nil, errors.New("会话ID不能为空")
	}
	if len(req.MsgIds) == 0 {
		return nil, errors.New("消息ID列表不能为空")
	}

	// 解析会话ID，确定会话类型和目标ID
	var convType int
	var targetId string

	if strings.Contains(req.ConvId, "-") {
		// 单聊会话
		convType = consts.ChatMsgType_Private
		parts := strings.Split(req.ConvId, "-")
		if len(parts) != 2 {
			return nil, errors.New("无效的会话ID格式")
		}

		// 确定目标ID（对方ID）
		if parts[0] == userId {
			targetId = parts[1]
		} else if parts[1] == userId {
			targetId = parts[0]
		} else {
			return nil, errors.New("您不是该会话的成员")
		}
	} else {
		// 群聊会话
		convType = consts.ChatMsgType_Group
		targetId = req.ConvId
	}

	// 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	// 验证消息是否属于该会话
	var validMsgIds []string
	var query *gorm.DB
	if convType == consts.ChatMsgType_Private {
		// 单聊：验证消息是否是对方发送给自己的
		query = tx.Model(&modelChat.ChatMessage{}).
			Select("msg_id").
			Where(
				"msg_id IN ? AND sender_id = ? AND receiver_id = ? AND deleted_at IS NULL",
				req.MsgIds, targetId, userId,
			)
	} else {
		// 群聊：验证消息是否属于该群
		query = tx.Model(&modelChat.ChatMessage{}).
			Select("msg_id").
			Where(
				"msg_id IN ? AND receiver_id = ? AND receiver_type = ? AND deleted_at IS NULL",
				req.MsgIds, targetId, convType,
			)
	}

	result := query.Pluck("msg_id", &validMsgIds)
	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 如果没有有效消息，直接返回
	if len(validMsgIds) == 0 {
		tx.Rollback()
		return &chat.MarkConvMsgReadRes{
			SuccessCount: 0,
		}, nil
	}

	// 更新消息接收状态
	now := time.Now()
	readTime := gtime.Now()
	result = tx.Model(&modelChat.ChatMessageReceiver{}).Where(
		"msg_id IN ? AND receiver_id = ? AND status < ?",
		validMsgIds, userId, consts.MessageStatus_Readed,
	).Updates(map[string]interface{}{
		"read_time":  readTime,
		"status":     consts.MessageStatus_Readed,
		"updated_at": now,
	})

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 获取更新的记录数
	var updatedCount int64 = result.RowsAffected

	// 如果有消息被标记为已读，更新会话的未读计数
	if updatedCount > 0 {
		// 直接更新会话的未读计数，使用SQL的GREATEST函数确保不会出现负数
		// 这样可以避免先查询再更新的两步操作，提高性能
		result = tx.Exec(
			"UPDATE chat_conversation SET unread_count = GREATEST(0, unread_count - ?), updated_at = ? WHERE user_id = ? AND target_id = ? AND conv_type = ?",
			updatedCount, now, userId, targetId, convType,
		)

		if result.Error != nil {
			tx.Rollback()
			return nil, result.Error
		}
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		return nil, err
	}

	// 返回结果
	res = &chat.MarkConvMsgReadRes{
		SuccessCount: int(updatedCount),
	}

	return
}

// MarkConvMsgRead 4、会话消息-标记已读(全部)
func (s *ServerMessage) MarkConvAllMsgRead(req *chat.MarkConvAllMsgReadReq, userId string) (res *chat.MarkConvAllMsgReadRes, err error) {
	// 参数校验
	if req.ConvId == "" {
		return nil, errors.New("会话ID不能为空")
	}

	// 解析会话ID，确定会话类型和目标ID
	var convType int
	var targetId string

	if strings.Contains(req.ConvId, "-") {
		// 单聊会话
		convType = consts.ChatMsgType_Private
		parts := strings.Split(req.ConvId, "-")
		if len(parts) != 2 {
			return nil, errors.New("无效的会话ID格式")
		}

		// 确定目标ID（对方ID）
		if parts[0] == userId {
			targetId = parts[1]
		} else if parts[1] == userId {
			targetId = parts[0]
		} else {
			return nil, errors.New("您不是该会话的成员")
		}
	} else {
		// 群聊会话
		convType = consts.ChatMsgType_Group
		targetId = req.ConvId
	}

	// 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	// 查找未读消息
	var msgIds []string
	var query *gorm.DB
	if convType == consts.ChatMsgType_Private {
		// 单聊：查询对方发送给自己的未读消息
		query = tx.Model(&modelChat.ChatMessageReceiver{}).
			Select("msg_id").
			Joins("JOIN chat_message ON chat_message.msg_id = chat_message_receiver.msg_id").
			Where(
				"chat_message_receiver.receiver_id = ? AND chat_message.sender_id = ? AND chat_message_receiver.is_read = ?",
				userId, targetId, false,
			)
	} else {
		// 群聊：查询群内的未读消息
		query = tx.Model(&modelChat.ChatMessageReceiver{}).
			Select("msg_id").
			Joins("JOIN chat_message ON chat_message.msg_id = chat_message_receiver.msg_id").
			Where(
				"chat_message_receiver.receiver_id = ? AND chat_message.receiver_id = ? AND chat_message.receiver_type = ? AND chat_message_receiver.status < ?",
				userId, targetId, convType, consts.MessageStatus_Readed,
			)
	}

	result := query.Pluck("msg_id", &msgIds)
	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 如果没有未读消息，直接返回
	if len(msgIds) == 0 {
		tx.Rollback()
		return &chat.MarkConvAllMsgReadRes{
			SuccessCount: 0,
		}, nil
	}

	// 更新消息接收状态
	now := time.Now()
	readTime := gtime.Now()
	result = tx.Model(&modelChat.ChatMessageReceiver{}).Where(
		"msg_id IN ? AND receiver_id = ?",
		msgIds, userId,
	).Updates(map[string]interface{}{
		"read_time":  readTime,
		"status":     consts.MessageStatus_Readed,
		"updated_at": now,
	})

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 更新会话未读数
	result = tx.Model(&modelChat.ChatConversation{}).Where(
		"user_id = ? AND target_id = ? AND conv_type = ?",
		userId, targetId, convType,
	).Update("unread_count", 0)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 提交事务
	tx.Commit()

	// 返回结果
	res = &chat.MarkConvAllMsgReadRes{
		SuccessCount: len(msgIds),
	}

	return
}

// 标记指定会话所有消息已读
/*func (s *ServerMessage) markConvAllMsgRead(req *chat.MarkConvAllMsgReadReq, userId string) (res *chat.MarkConvAllMsgReadRes, err error) {

}

// markAllConvRead 标记所有会话的所有消息为已读
func (s *ServerMessage) markAllConvAllMsgRead(req *chat.MarkConvAllMsgReadReq, userId string) (res *chat.MarkConvAllMsgReadRes, err error) {
	// 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	// 查找用户的所有会话
	var conversations []modelChat.ChatConversation
	result := tx.Model(&modelChat.ChatConversation{}).Where(
		"user_id = ? AND deleted_at IS NULL AND unread_count > 0",
		userId,
	).Find(&conversations)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 如果没有会话，直接返回
	if len(conversations) == 0 {
		tx.Rollback()
		return &chat.MarkAllConvReadRes{
			SuccessCount: 0,
		}, nil
	}

	// 收集所有需要更新的会话ID和目标ID
	var convIds []string
	convTargetMap := make(map[string]string) // 会话ID -> 目标ID
	convTypeMap := make(map[string]int)      // 会话ID -> 会话类型

	for _, conv := range conversations {
		convIds = append(convIds, conv.ConvId)
		convTargetMap[conv.ConvId] = conv.TargetId
		convTypeMap[conv.ConvId] = conv.ConvType
	}

	// 批量更新所有会话的未读计数
	now := time.Now()
	result = tx.Model(&modelChat.ChatConversation{}).Where(
		"user_id = ? AND conv_id IN ?",
		userId, convIds,
	).Updates(map[string]interface{}{
		"unread_count": 0,
		"updated_at":   now,
	})

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 批量更新所有未读消息的状态
	// 使用单个SQL语句更新所有未读消息，提高性能
	readTime := gtime.Now()

	// 构建更新语句
	updateSQL := `
		UPDATE chat_message_receiver
		SET status = ?, read_time = ?, updated_at = ?
		WHERE receiver_id = ? AND status < ? AND msg_id IN (
			SELECT cmr.msg_id
			FROM chat_message_receiver cmr
			JOIN chat_message cm ON cm.msg_id = cmr.msg_id
			JOIN chat_conversation cc ON (cc.user_id = cmr.receiver_id AND cc.target_id = CASE
				WHEN cm.receiver_type = 1 THEN cm.sender_id
				ELSE cm.receiver_id
			END AND cc.conv_type = cm.receiver_type)
			WHERE cmr.receiver_id = ? AND cc.conv_id IN ? AND cm.deleted_at IS NULL AND cmr.status < ?
		)
	`

	// 执行更新
	result = tx.Exec(
		updateSQL,
		consts.MessageStatus_Readed, readTime, now,
		userId, consts.MessageStatus_Readed,
		userId, convIds, consts.MessageStatus_Readed,
	)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 获取更新的消息数
	totalUpdatedMsgCount := result.RowsAffected

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		return nil, err
	}

	// 返回结果
	res = &chat.MarkAllConvReadRes{
		SuccessCount: int(totalUpdatedMsgCount),
	}

	return
}
*/
// GetUnreadCount 5、未读消息数-获取
func (s *ServerMessage) GetUnreadCount(req *chat.GetUnreadCountReq, userId string) (res *chat.GetUnreadCountRes, err error) {
	// 构建查询条件
	query := dao.Db.Model(&modelChat.ChatConversation{}).Where(
		"user_id = ? AND deleted_at IS NULL AND unread_count > 0",
		userId,
	)

	// 如果指定了会话ID，则只查询该会话
	if req.ConvId != "" {
		query = query.Where("conv_id = ?", req.ConvId)
	}

	// 如果指定了目标类型，则按类型筛选
	if req.ConvType > 0 {
		query = query.Where("conv_type = ?", req.ConvType)
	}

	// 获取未读消息总数
	var totalUnreadCount int64
	result := query.Select("COALESCE(SUM(unread_count), 0)").Scan(&totalUnreadCount)
	if result.Error != nil {
		return nil, result.Error
	}

	// 获取未读消息列表 - 使用全新的DB实例，避免受到前面Select语句的影响
	listQuery := dao.Db.Session(&gorm.Session{}).Model(&modelChat.ChatConversation{}).Where(
		"user_id = ? AND deleted_at IS NULL AND unread_count > 0",
		userId,
	)

	// 应用相同的过滤条件
	if req.ConvId != "" {
		listQuery = listQuery.Where("conv_id = ?", req.ConvId)
	}
	if req.ConvType > 0 {
		listQuery = listQuery.Where("conv_type = ?", req.ConvType)
	}

	// 执行查询获取会话列表
	var conversations []modelChat.ChatConversation
	result = listQuery.Find(&conversations)
	if result.Error != nil {
		return nil, result.Error
	}

	// 构建响应数据
	var unreadList []chat.UnreadCountInfo
	for _, conv := range conversations {
		unreadInfo := chat.UnreadCountInfo{
			ConvId:      conv.ConvId,
			ConvType:    conv.ConvType,
			UnreadCount: conv.UnreadCount,
			LastMsgId:   conv.LastMsgId,
			LastMsgTime: tools.GtimeToStringNMDHMS(conv.LastMsgTime),
		}
		unreadList = append(unreadList, unreadInfo)
	}

	// 返回结果
	res = &chat.GetUnreadCountRes{
		List:       unreadList,
		TotalCount: int(totalUnreadCount),
	}

	return
}
