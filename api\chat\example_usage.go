package chat

import (
    "fmt"
    "strings"
)

// ==================== 外部文档引用使用示例 ====================
//
// 这个文件展示了如何使用外部文档引用功能
// 注意：这只是示例代码，实际使用时请根据需要调整

/*
// 示例1：简单接口使用外部文档
type SimpleApiReq struct {
    g.Meta `path:"/simple" tags:"示例" method:"get" summary:"简单接口示例"`

    Param1 string `json:"param1" dc:"参数1"`
    Param2 int    `json:"param2" dc:"参数2"`
}

// 对应的文档文件：docs/simple-api.md
// # 简单接口示例
//
// ## 功能说明
// 这是一个简单的API接口示例
//
// ## 请求参数
// | 参数名 | 类型 | 必填 | 说明 |
// |--------|------|------|------|
// | param1 | string | 是 | 参数1说明 |
// | param2 | int | 否 | 参数2说明 |

// 示例2：复杂接口使用外部文档
type ComplexApiReq struct {
    g.Meta `path:"/complex" tags:"示例" method:"post" summary:"复杂接口示例"`

    // 复杂的请求结构
    Data ComplexData `json:"data" dc:"复杂数据结构"`
}

type ComplexData struct {
    Field1 string   `json:"field1" dc:"字段1"`
    Field2 []string `json:"field2" dc:"字段2"`
    Field3 map[string]interface{} `json:"field3" dc:"字段3"`
}

// 对应的文档文件：docs/complex-api.md
// # 复杂接口示例
//
// ## 功能说明
// 这是一个复杂的API接口示例，包含嵌套数据结构
//
// ## 请求示例
// ```json
// {
//   "data": {
//     "field1": "value1",
//     "field2": ["item1", "item2"],
//     "field3": {
//       "key1": "value1",
//       "key2": 123
//     }
//   }
// }
// ```

// 示例3：使用常量引用文档（如果需要在多处使用）
var (
    CommonApiDoc = GetDocContent("common-api.md")
)

type CommonApiReq struct {
    g.Meta `path:"/common" tags:"示例" method:"post" summary:"通用接口示例"`

    CommonField string `json:"common_field" dc:"通用字段"`
}

// 示例4：文档内容预览
func ExampleDocContent() {
    // 获取文档内容
    wsLoginContent := GetDocContent("ws-login.md")
    testApiContent := GetDocContent("test-api.md")
    messageStatusContent := GetDocContent("message-status.md")

    // 打印文档内容（用于调试）
    println("WebSocket登录文档长度:", len(wsLoginContent))
    println("测试接口文档长度:", len(testApiContent))
    println("消息状态文档长度:", len(messageStatusContent))
}

// 示例5：错误处理
func ExampleErrorHandling() {
    // 尝试加载不存在的文档
    content := GetDocContent("non-existent.md")

    // 检查是否是错误信息
    if len(content) > 0 && content[:4] == "文档加载失败" {
        println("文档加载失败，使用默认描述")
        // 可以使用默认的简短描述
    }
}
*/

// ==================== 实际使用的文档常量 ====================

// 这些常量在编译时会被初始化，包含转换后的HTML内容
// 可以在接口定义中直接使用，但由于Go语言限制，
// 仍然需要将内容复制到description标签中

// 使用示例：
// 1. 编辑 docs/your-api.md 文件
// 2. 运行程序，GetDocContent("your-api.md") 会返回转换后的HTML
// 3. 将HTML内容复制到接口的description标签中
// 4. 或者使用代码生成工具自动完成这个过程

// 文档内容示例（实际内容会更长）
const (
    ExampleShortDoc = `<h3>示例接口</h3><p>这是一个示例接口的简短描述</p>`

    ExampleLongDoc = `<h3>复杂接口示例</h3>
<p>这是一个复杂接口的详细描述，包含多个部分：</p>
<h4>功能说明</h4>
<p>详细的功能说明...</p>
<h4>请求参数</h4>
<table border="1">
<tr><th>参数名</th><th>类型</th><th>说明</th></tr>
<tr><td>param1</td><td>string</td><td>参数说明</td></tr>
</table>`
)

// ==================== 开发工具函数 ====================

// PrintDocContent 打印文档内容（开发调试用）
func PrintDocContent(filename string) {
    content := GetDocContent(filename)
    println("=== 文档内容 ===")
    println("文件名:", filename)
    println("内容长度:", len(content))
    println("内容预览:")
    if len(content) > 200 {
        println(content[:200] + "...")
    } else {
        println(content)
    }
    println("=== 结束 ===")
}

// ValidateDocFiles 验证所有文档文件是否存在
func ValidateDocFiles() map[string]bool {
    files := []string{
        "ws-login.md",
        "test-api.md",
        "message-status.md",
        "README.md",
    }

    results := make(map[string]bool)
    for _, file := range files {
        content := GetDocContent(file)
        // 如果内容以"文档加载失败"开头，说明文件不存在
        results[file] = !strings.HasPrefix(content, "文档加载失败")
    }

    return results
}

// GenerateDocConstants 生成文档常量代码（开发工具）
func GenerateDocConstants() string {
    files := []string{
        "ws-login.md",
        "test-api.md",
        "message-status.md",
    }

    var code strings.Builder
    code.WriteString("// 自动生成的文档常量\n")
    code.WriteString("var (\n")

    for _, file := range files {
        content := GetDocContent(file)
        varName := strings.ReplaceAll(file, "-", "")
        varName = strings.ReplaceAll(varName, ".md", "Doc")
        varName = strings.Title(varName)

        code.WriteString(fmt.Sprintf("    %s = `%s`\n", varName, content))
    }

    code.WriteString(")\n")
    return code.String()
}
