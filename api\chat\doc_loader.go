package chat

import (
	"embed"
	"regexp"
	"strings"
)

// 嵌入文档文件到二进制中
//
//go:embed docs/*.md
var docsFS embed.FS

// DocLoader 文档加载器
type DocLoader struct {
	cache map[string]string
}

// NewDocLoader 创建文档加载器
func NewDocLoader() *DocLoader {
	return &DocLoader{
		cache: make(map[string]string),
	}
}

// LoadDoc 加载文档并转换为HTML
func (dl *DocLoader) LoadDoc(filename string) string {
	// 检查缓存
	if content, exists := dl.cache[filename]; exists {
		return content
	}

	// 读取文件
	content, err := docsFS.ReadFile("docs/" + filename)
	if err != nil {
		return "文档加载失败: " + err.Error()
	}

	// 转换Markdown到HTML
	htmlContent := dl.markdownToHTML(string(content))

	// 缓存结果
	dl.cache[filename] = htmlContent

	return htmlContent
}

// markdownToHTML 简单的Markdown到HTML转换器
func (dl *DocLoader) markdownToHTML(markdown string) string {
	html := markdown

	// 1. 转换标题
	html = regexp.MustCompile(`^# (.+)$`).ReplaceAllString(html, `<h3>$1</h3>`)
	html = regexp.MustCompile(`^## (.+)$`).ReplaceAllString(html, `<h4>$1</h4>`)
	html = regexp.MustCompile(`^### (.+)$`).ReplaceAllString(html, `<h5>$1</h5>`)

	// 2. 转换代码块
	codeBlockRegex := regexp.MustCompile("```([a-zA-Z]*)\n([\\s\\S]*?)\n```")
	html = codeBlockRegex.ReplaceAllStringFunc(html, func(match string) string {
		parts := codeBlockRegex.FindStringSubmatch(match)
		if len(parts) >= 3 {
			language := parts[1]
			code := parts[2]
			// 转义HTML特殊字符
			escapedCode := strings.ReplaceAll(code, "<", "&lt;")
			escapedCode = strings.ReplaceAll(escapedCode, ">", "&gt;")
			escapedCode = strings.ReplaceAll(escapedCode, "\"", "&quot;")

			if language != "" {
				return `<pre><code class="language-` + language + `">` + escapedCode + `</code></pre>`
			}
			return `<pre><code>` + escapedCode + `</code></pre>`
		}
		return match
	})

	// 3. 转换行内代码
	html = regexp.MustCompile("`([^`]+)`").ReplaceAllString(html, `<code>$1</code>`)

	// 4. 转换粗体
	html = regexp.MustCompile(`\*\*([^*]+)\*\*`).ReplaceAllString(html, `<strong>$1</strong>`)

	// 5. 转换表格
	html = dl.convertTables(html)

	// 6. 转换列表
	html = dl.convertLists(html)

	// 7. 转换段落
	html = dl.convertParagraphs(html)

	// 8. 清理多余的换行
	html = regexp.MustCompile(`\n\s*\n`).ReplaceAllString(html, "\n")
	html = strings.TrimSpace(html)

	return html
}

// convertTables 转换表格
func (dl *DocLoader) convertTables(content string) string {
	lines := strings.Split(content, "\n")
	var result []string
	var inTable bool
	var tableRows []string

	for i, line := range lines {
		line = strings.TrimSpace(line)

		// 检查是否是表格行
		if strings.Contains(line, "|") && !strings.HasPrefix(line, "```") {
			if !inTable {
				inTable = true
				tableRows = []string{}
			}
			tableRows = append(tableRows, line)

			// 检查下一行是否还是表格
			if i+1 >= len(lines) || !strings.Contains(strings.TrimSpace(lines[i+1]), "|") {
				// 表格结束，生成HTML
				result = append(result, dl.generateTableHTML(tableRows))
				inTable = false
				tableRows = nil
			}
		} else {
			if inTable {
				// 表格意外结束
				result = append(result, dl.generateTableHTML(tableRows))
				inTable = false
				tableRows = nil
			}
			result = append(result, line)
		}
	}

	return strings.Join(result, "\n")
}

// generateTableHTML 生成表格HTML
func (dl *DocLoader) generateTableHTML(rows []string) string {
	if len(rows) == 0 {
		return ""
	}

	var html strings.Builder
	html.WriteString(`<table border="1" style="border-collapse: collapse; width: 100%;">`)

	for i, row := range rows {
		row = strings.Trim(row, "|")
		cells := strings.Split(row, "|")

		// 跳过分隔行（包含 --- 的行）
		if strings.Contains(row, "---") {
			continue
		}

		if i == 0 || (i == 1 && strings.Contains(rows[1], "---")) {
			// 表头
			html.WriteString("<tr>")
			for _, cell := range cells {
				html.WriteString("<th>")
				html.WriteString(strings.TrimSpace(cell))
				html.WriteString("</th>")
			}
			html.WriteString("</tr>")
		} else {
			// 表格内容
			html.WriteString("<tr>")
			for _, cell := range cells {
				html.WriteString("<td>")
				html.WriteString(strings.TrimSpace(cell))
				html.WriteString("</td>")
			}
			html.WriteString("</tr>")
		}
	}

	html.WriteString("</table>")
	return html.String()
}

// convertLists 转换列表
func (dl *DocLoader) convertLists(content string) string {
	lines := strings.Split(content, "\n")
	var result []string
	var inList bool
	var listItems []string
	var isOrdered bool

	for _, line := range lines {
		trimmed := strings.TrimSpace(line)

		// 检查是否是列表项
		if strings.HasPrefix(trimmed, "- ") || strings.HasPrefix(trimmed, "* ") {
			if !inList {
				inList = true
				isOrdered = false
				listItems = []string{}
			}
			listItems = append(listItems, strings.TrimPrefix(strings.TrimPrefix(trimmed, "- "), "* "))
		} else if regexp.MustCompile(`^\d+\. `).MatchString(trimmed) {
			if !inList {
				inList = true
				isOrdered = true
				listItems = []string{}
			}
			listItems = append(listItems, regexp.MustCompile(`^\d+\. `).ReplaceAllString(trimmed, ""))
		} else {
			if inList {
				// 列表结束
				if isOrdered {
					result = append(result, "<ol>")
				} else {
					result = append(result, "<ul>")
				}

				for _, item := range listItems {
					result = append(result, "<li>"+item+"</li>")
				}

				if isOrdered {
					result = append(result, "</ol>")
				} else {
					result = append(result, "</ul>")
				}

				inList = false
				listItems = nil
			}
			result = append(result, line)
		}
	}

	// 处理文件末尾的列表
	if inList {
		if isOrdered {
			result = append(result, "<ol>")
		} else {
			result = append(result, "<ul>")
		}

		for _, item := range listItems {
			result = append(result, "<li>"+item+"</li>")
		}

		if isOrdered {
			result = append(result, "</ol>")
		} else {
			result = append(result, "</ul>")
		}
	}

	return strings.Join(result, "\n")
}

// convertParagraphs 转换段落
func (dl *DocLoader) convertParagraphs(content string) string {
	lines := strings.Split(content, "\n")
	var result []string

	for _, line := range lines {
		trimmed := strings.TrimSpace(line)

		// 跳过空行和已经是HTML标签的行
		if trimmed == "" ||
			strings.HasPrefix(trimmed, "<") ||
			strings.HasPrefix(trimmed, "#") {
			result = append(result, line)
		} else {
			// 普通文本行转换为段落
			result = append(result, "<p>"+trimmed+"</p>")
		}
	}

	return strings.Join(result, "\n")
}

// 全局文档加载器实例
var docLoader = NewDocLoader()

// GetDocContent 获取文档内容（供外部调用）
func GetDocContent(filename string) string {
	return docLoader.LoadDoc(filename)
}
