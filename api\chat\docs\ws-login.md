# 🔗 WebSocket聊天长连接登录接口

## 连接方式
**WebSocket URL：** `ws://{host}:{port}/chat/ws-login`  
**认证方式：** 请求头携带 `Authorization: AyjChat <token>`

## 📤 客户端发送消息格式
```json
{
  "msg_type": "chat",
  "msg_client_id": "client_msg_001", 
  "msg_receiver_id": "user_123",
  "msg_receiver_type": 1,
  "msg_content_fmt": 1,
  "msg_content_type": "text",
  "msg_content": "Hello World"
}
```

## 📥 服务端响应格式
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "msg_id": "msg_001",
    "msg_status": 2,
    "send_time": "2025-01-20 10:30:00",
    "sender_id": "user_456",
    "sender_nick": "张三",
    "conv_id": "user_123-user_456"
  }
}
```

## 🔄 消息类型说明
- **chat**：普通聊天消息
- **chat_notice**：聊天通知消息（如：用户加入群聊、退出群聊等）
- **system**：系统消息（如：系统维护通知、版本更新等）
- **ping**：心跳消息（保持连接活跃）

## 📋 接收者类型说明
| 类型值 | 说明 | receiver_id格式 | 示例 |
|--------|------|----------------|------|
| 1 | 单聊 | 对方用户ID | user_123 |
| 2 | 群聊 | 群组ID | group_456 |

## ⚠️ 重要注意事项
1. **连接保持：** 建议每30秒发送一次ping消息保持连接
2. **消息大小：** 单个消息内容大小限制为10MB
3. **连接超时：** 无活动连接将在5分钟后自动断开
4. **重连机制：** 连接断开后客户端应实现自动重连
5. **消息去重：** 使用msg_client_id进行客户端消息去重

## 🚀 JavaScript使用示例
```javascript
// 建立WebSocket连接
const ws = new WebSocket('ws://localhost:8000/chat/ws-login', [], {
  headers: {
    'Authorization': 'AyjChat your_token_here'
  }
});

// 连接成功
ws.onopen = function() {
  console.log('WebSocket连接已建立');
  
  // 发送单聊消息
  ws.send(JSON.stringify({
    msg_type: 'chat',
    msg_client_id: 'client_' + Date.now(),
    msg_receiver_id: 'user_123',
    msg_receiver_type: 1,
    msg_content_fmt: 1,
    msg_content_type: 'text',
    msg_content: 'Hello, 这是一条测试消息！'
  }));
};

// 接收消息
ws.onmessage = function(event) {
  const response = JSON.parse(event.data);
  console.log('收到消息:', response);
  
  if (response.code === 0) {
    console.log('消息发送成功:', response.data);
  } else {
    console.error('消息发送失败:', response.message);
  }
};

// 连接错误处理
ws.onerror = function(error) {
  console.error('WebSocket错误:', error);
};

// 连接关闭处理
ws.onclose = function() {
  console.log('WebSocket连接已关闭');
  // 实现重连逻辑
  setTimeout(() => {
    console.log('尝试重新连接...');
    // 重新建立连接
  }, 3000);
};

// 心跳保持
setInterval(() => {
  if (ws.readyState === WebSocket.OPEN) {
    ws.send(JSON.stringify({
      msg_type: 'ping',
      msg_client_id: 'ping_' + Date.now()
    }));
  }
}, 30000);
```

## 🐍 Python使用示例
```python
import websocket
import json
import time
import threading

class ChatWebSocket:
    def __init__(self, url, token):
        self.url = url
        self.token = token
        self.ws = None
        
    def on_message(self, ws, message):
        response = json.loads(message)
        print(f"收到消息: {response}")
        
        if response.get('code') == 0:
            print(f"消息处理成功: {response.get('data')}")
        else:
            print(f"消息处理失败: {response.get('message')}")
    
    def on_error(self, ws, error):
        print(f"WebSocket错误: {error}")
    
    def on_close(self, ws, close_status_code, close_msg):
        print("WebSocket连接已关闭")
    
    def on_open(self, ws):
        print("WebSocket连接已建立")
        
        # 发送测试消息
        self.send_message('user_123', 1, 'text', 'Hello from Python!')
        
        # 启动心跳
        self.start_heartbeat()
    
    def send_message(self, receiver_id, receiver_type, content_type, content):
        message = {
            "msg_type": "chat",
            "msg_client_id": f"python_client_{int(time.time() * 1000)}",
            "msg_receiver_id": receiver_id,
            "msg_receiver_type": receiver_type,
            "msg_content_fmt": 1,
            "msg_content_type": content_type,
            "msg_content": content
        }
        self.ws.send(json.dumps(message))
    
    def start_heartbeat(self):
        def heartbeat():
            while self.ws and self.ws.sock and self.ws.sock.connected:
                try:
                    ping_message = {
                        "msg_type": "ping",
                        "msg_client_id": f"ping_{int(time.time() * 1000)}"
                    }
                    self.ws.send(json.dumps(ping_message))
                    time.sleep(30)
                except:
                    break
        
        threading.Thread(target=heartbeat, daemon=True).start()
    
    def connect(self):
        websocket.enableTrace(True)
        self.ws = websocket.WebSocketApp(
            self.url,
            header={"Authorization": f"AyjChat {self.token}"},
            on_open=self.on_open,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close
        )
        self.ws.run_forever()

# 使用示例
if __name__ == "__main__":
    chat_ws = ChatWebSocket(
        "ws://localhost:8000/chat/ws-login",
        "your_token_here"
    )
    chat_ws.connect()
```

## 📱 移动端注意事项
- **Android：** 需要在网络安全配置中允许明文传输（开发环境）
- **iOS：** 需要在Info.plist中配置App Transport Security
- **小程序：** 需要在管理后台配置WebSocket域名白名单
- **React Native：** 建议使用react-native-websocket库

## 🔧 错误码说明
| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 1001 | 认证失败 | 检查token是否正确或已过期 |
| 1002 | 消息格式错误 | 检查JSON格式是否正确 |
| 1003 | 接收者不存在 | 检查用户ID或群组ID是否正确 |
| 1004 | 权限不足 | 检查是否有发送消息的权限 |
| 1005 | 消息内容过大 | 减少消息内容大小 |
