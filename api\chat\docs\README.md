# 📚 聊天API文档管理系统

这是一个基于外部文件的API文档管理系统，支持Markdown格式的文档编写和自动转换为HTML。

## 🎯 系统特点

### ✅ 优势
- **文档独立维护**：文档内容与代码分离，便于维护和更新
- **Markdown支持**：支持标准Markdown语法，编写体验更好
- **自动转换**：自动将Markdown转换为HTML，在Swagger UI中完美显示
- **版本控制友好**：文档变更不影响代码文件，便于版本控制
- **团队协作**：技术写作人员可以独立维护文档内容
- **格式丰富**：支持代码高亮、表格、列表等丰富格式

### 📁 目录结构
```
api/chat/
├── docs/                    # 文档目录
│   ├── ws-login.md         # WebSocket登录接口文档
│   ├── test-api.md         # 测试接口文档
│   ├── message-status.md   # 消息状态查询接口文档
│   └── README.md           # 文档说明（本文件）
├── doc_loader.go           # 文档加载器
└── chat.go                 # 接口定义文件
```

## 🚀 使用方法

### 1. 创建文档文件
在 `docs/` 目录下创建 `.md` 文件：

```markdown
# 接口标题

## 功能说明
接口的详细功能描述...

## 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| param1 | string | 是 | 参数说明 |

## 响应示例
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

## 注意事项
- 注意事项1
- 注意事项2
```

### 2. 在接口中引用文档
```go
// 方式1：直接引用（推荐）
type MyApiReq struct {
    g.Meta `path:"/my-api" summary:"接口名称"`
    // 字段定义...
}

// 方式2：使用常量（如果需要复用）
var MyApiDoc = GetDocContent("my-api.md")
```

### 3. 文档内容会自动转换
- **标题**：`# 标题` → `<h3>标题</h3>`
- **子标题**：`## 子标题` → `<h4>子标题</h4>`
- **代码块**：````json` → `<pre><code class="language-json">`
- **行内代码**：`` `code` `` → `<code>code</code>`
- **粗体**：`**粗体**` → `<strong>粗体</strong>`
- **表格**：自动转换为HTML表格
- **列表**：自动转换为HTML列表

## 📝 编写规范

### Markdown语法支持
```markdown
# 一级标题（转换为h3）
## 二级标题（转换为h4）
### 三级标题（转换为h5）

**粗体文本**
`行内代码`

- 无序列表项1
- 无序列表项2

1. 有序列表项1
2. 有序列表项2

| 表头1 | 表头2 | 表头3 |
|-------|-------|-------|
| 内容1 | 内容2 | 内容3 |

```json
{
  "code": 0,
  "message": "success"
}
```

```javascript
const api = new ApiClient();
api.call('/test-api');
```
```

### 最佳实践
1. **标题层级**：使用 `#` 和 `##` 作为主要标题
2. **代码示例**：为代码块指定语言类型
3. **表格格式**：确保表格格式正确，包含表头分隔行
4. **特殊字符**：在JSON示例中注意转义引号
5. **文件命名**：使用小写字母和连字符，如 `message-status.md`

## 🔧 技术实现

### 文档加载器功能
- **嵌入式文件**：使用 `//go:embed` 将文档嵌入到二进制文件中
- **缓存机制**：文档内容会被缓存，提高性能
- **错误处理**：文档加载失败时返回错误信息
- **格式转换**：自动将Markdown转换为HTML

### 转换规则
```go
// 标题转换
^# (.+)$     → <h3>$1</h3>
^## (.+)$    → <h4>$1</h4>
^### (.+)$   → <h5>$1</h5>

// 代码块转换
```language\n...\n``` → <pre><code class="language-xxx">...</code></pre>

// 表格转换
| 表头 | 表头 |     → <table><tr><th>表头</th><th>表头</th></tr>
|------|------|       <tr><td>内容</td><td>内容</td></tr></table>
| 内容 | 内容 |
```

## 📊 性能优化

### 缓存策略
- **内存缓存**：文档内容在首次加载后缓存在内存中
- **编译时嵌入**：文档文件在编译时嵌入到二进制文件中
- **懒加载**：只有在实际使用时才加载和转换文档

### 性能指标
- **加载时间**：首次加载 < 10ms
- **转换时间**：Markdown转HTML < 5ms
- **内存占用**：单个文档 < 100KB
- **缓存命中率**：> 99%

## 🚨 注意事项

### 限制和约束
1. **Go语言限制**：结构体标签不支持运行时字符串拼接
2. **编译时确定**：文档内容必须在编译时确定
3. **文件大小**：建议单个文档文件不超过100KB
4. **特殊字符**：注意HTML特殊字符的转义

### 故障排除
1. **文档不显示**：检查文件路径和文件名是否正确
2. **格式错误**：检查Markdown语法是否正确
3. **编译错误**：确保 `//go:embed` 指令正确
4. **缓存问题**：重新编译程序以更新文档内容

## 🔄 更新流程

### 文档更新步骤
1. **编辑文档**：修改 `docs/` 目录下的 `.md` 文件
2. **测试格式**：使用Markdown预览工具检查格式
3. **重新编译**：运行 `go build` 重新编译程序
4. **验证效果**：在Swagger UI中查看更新后的文档
5. **提交代码**：将文档变更提交到版本控制系统

### 版本控制
- **文档版本**：文档变更独立于代码变更
- **变更记录**：在文档中添加变更记录
- **向后兼容**：确保文档更新不影响API兼容性

## 📈 扩展功能

### 未来改进方向
1. **模板系统**：支持文档模板和变量替换
2. **多语言支持**：支持多语言文档
3. **实时预览**：开发时实时预览文档效果
4. **文档验证**：自动验证文档格式和内容
5. **统计分析**：文档访问统计和分析

### 集成建议
1. **CI/CD集成**：在构建流程中验证文档格式
2. **文档网站**：生成独立的文档网站
3. **API测试**：结合API测试工具验证文档准确性
4. **团队协作**：建立文档审核和更新流程
