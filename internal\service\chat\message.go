/*
******		FileName	:	message.go
******		Describe	:	此文件主要用于聊天消息数据的管理
******		Date		:	2025-05-10
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   聊天 数据保存与获取的实现
 */

package server

import (
	"ayj_chat_back/api/chat"
	"ayj_chat_back/api/user"
	"ayj_chat_back/internal/consts"
	"ayj_chat_back/internal/dao"
	"ayj_chat_back/internal/model/chat"
	"ayj_chat_back/internal/model/user"
	"ayj_chat_back/internal/public/tools"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"gorm.io/gorm"
	"strings"
	"time"
)

// ServerMessage 消息服务结构体
type ServerMessage struct {
	// 分表策略
	shardCount int8 // 分表数量
}

// MessageWithReceiver 消息与接收状态的组合结构体（GetMsgListForConvId和GetMsgConvList共用）
type MessageWithReceiver struct {
	// 消息基本信息
	MsgId          string      `gorm:"column:msg_id"`
	MsgType        string      `gorm:"column:msg_type"`
	SenderId       string      `gorm:"column:sender_id"`
	SendTime       *gtime.Time `gorm:"column:send_time"`
	MsgContentFmt  int8        `gorm:"column:msg_content_fmt"`
	MsgContentType string      `gorm:"column:msg_content_type"`
	MsgContent     string      `gorm:"column:msg_content"`

	// 接收状态信息（从ChatMessageReceiver表获取）
	ReceiverStatus *int8       `gorm:"column:receiver_status"`
	ReadTime       *gtime.Time `gorm:"column:read_time"`
}

type ConversationWithLastMsg struct {
	// 会话基本信息
	modelChat.ChatConversation
	// 最后一条消息信息（复用MessageWithReceiver的逻辑）
	LastMsgType        string      `gorm:"column:last_msg_type"`
	LastMsgContentFmt  int8        `gorm:"column:last_msg_content_fmt"`
	LastMsgContentType string      `gorm:"column:last_msg_content_type"`
	LastMsgContent     string      `gorm:"column:last_msg_content"`
	LastMsgSenderId    string      `gorm:"column:last_msg_sender_id"`
	LastMsgSendTime    *gtime.Time `gorm:"column:last_msg_send_time"`
	// 接收状态信息（从ChatMessageReceiver表获取，与MessageWithReceiver保持一致）
	LastMsgReceiverStatus *int8       `gorm:"column:last_msg_receiver_status"`
	LastMsgReadTime       *gtime.Time `gorm:"column:last_msg_read_time"`
}

// GetMsgConvList 1、会话列表-获取
func (s *ServerMessage) GetMsgConvList(req *chat.GetMsgConvListReq, userId string) (res *chat.GetMsgConvListRes, err error) {
	// 1. 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 || req.Size > 100 {
		req.Size = 100
	}

	// 2. 构建查询条件
	query := dao.Db.Model(&modelChat.ChatConversation{}).
		Where("user_id = ? AND deleted_at IS NULL", userId)

	// 根据会话类型筛选
	if req.ConvType > 0 {
		query = query.Where("conv_type = ?", req.ConvType)
	}

	// 3. 获取会话总数和总未读数（单次查询优化）
	type CountResult struct {
		TotalCount       int64
		TotalUnreadCount int64
	}
	var countResult CountResult

	// 构建SQL查询
	var sqlQuery string
	var sqlParams []interface{}

	if req.ConvType > 0 {
		// 如果指定了会话类型
		sqlQuery = `
			SELECT
				COUNT(*) as total_count,
				COALESCE(SUM(unread_count), 0) as total_unread_count
			FROM chat_conversations
			WHERE user_id = ? AND deleted_at IS NULL
			AND conv_type = ?`
		sqlParams = []interface{}{userId, req.ConvType}
	} else {
		// 如果没有指定会话类型
		sqlQuery = `
			SELECT
				COUNT(*) as total_count,
				COALESCE(SUM(unread_count), 0) as total_unread_count
			FROM chat_conversations
			WHERE user_id = ? AND deleted_at IS NULL`
		sqlParams = []interface{}{userId}
	}

	// 执行查询
	result := dao.Db.Raw(sqlQuery, sqlParams...).Scan(&countResult)

	if result.Error != nil {
		return nil, result.Error
	}

	// 4. 获取会话列表和最后一条消息内容

	var conversationsWithMsg []ConversationWithLastMsg

	// 使用JOIN查询获取会话和最后一条消息的详细信息（包含正确的接收状态）
	queryBuilder := dao.Db.Table("chat_conversations as cc").
		Select(`
			cc.*,
			cm.msg_type as last_msg_type,
			cm.msg_content_fmt as last_msg_content_fmt,
			cm.msg_content_type as last_msg_content_type,
			cm.msg_content as last_msg_content,
			cm.sender_id as last_msg_sender_id,
			cm.send_time as last_msg_send_time,
			cmr.status as last_msg_receiver_status,
			cmr.read_time as last_msg_read_time
		`).
		Joins("LEFT JOIN chat_messages as cm ON cc.last_msg_id = cm.msg_id").
		Joins(`LEFT JOIN chat_message_receivers as cmr ON cm.msg_id = cmr.msg_id
			AND cmr.receiver_id = CASE
				WHEN cm.sender_id = ? THEN cm.receiver_id
				ELSE ?
			END`, userId, userId).
		Where("cc.user_id = ? AND cc.deleted_at IS NULL", userId)

	// 根据会话类型筛选
	if req.ConvType > 0 {
		queryBuilder = queryBuilder.Where("cc.conv_type = ?", req.ConvType)
	}

	// 执行查询
	result = queryBuilder.Order("CASE WHEN cc.status = 2 THEN 1 ELSE 0 END DESC, cc.last_msg_time DESC"). // 置顶的排在前面，然后按最后消息时间倒序
														Offset((req.Page - 1) * req.Size).
														Limit(req.Size).
														Find(&conversationsWithMsg)

	if result.Error != nil {
		return nil, result.Error
	}

	// 5. 获取会话昵称和头像信息
	convInfoMaps := s.getConvNickAndAvatar(conversationsWithMsg, userId)

	// 7. 构建响应数据
	convList := make([]chat.MsgConvInfo, 0, len(conversationsWithMsg))

	for _, conv := range conversationsWithMsg {
		// 构建最后一条消息信息（使用共用函数）
		var lastMsgInfo chat.MessageInfo
		if conv.LastMsgId != "" {
			lastMsgInfo = s.buildMessageInfo(
				conv.LastMsgId,
				conv.LastMsgType,
				conv.LastMsgSenderId,
				conv.LastMsgSendTime,
				conv.LastMsgContentFmt,
				conv.LastMsgContentType,
				conv.LastMsgContent,
				conv.LastMsgReceiverStatus,
				conv.LastMsgReadTime,
				conv.ConvId,
				conv.ConvType,
				userId,
				convInfoMaps.UserInfoMap,
				nil, // 会话列表中不需要群聊接收者状态
			)
		}

		// 获取会话昵称和头像
		convNick, convAvatar := s.getConvDisplayInfo(conv.ConvType, conv.TargetId, convInfoMaps)

		// 构建会话信息
		convInfo := chat.MsgConvInfo{
			ConvId:      conv.ConvId,
			ConvType:    conv.ConvType,
			TargetId:    conv.TargetId,
			UnreadCount: conv.UnreadCount,
			Status:      conv.Status,
			ConvNick:    convNick,
			ConvAvatar:  convAvatar,
			LastMsgInfo: lastMsgInfo,
		}

		convList = append(convList, convInfo)
	}

	// 6. 返回结果
	res = &chat.GetMsgConvListRes{
		List:             convList,
		TotalUnreadCount: int(countResult.TotalUnreadCount),
		HasMore:          int(countResult.TotalCount) > (req.Page * req.Size),
	}

	return res, nil
}

// GetMsgListForConvId 2、会话消息获取
func (s *ServerMessage) GetMsgListForConvId(req *chat.GetMsgListForConvIdReq, userId string) (res *chat.GetMsgListForConvIdRes, err error) {
	// 1. 参数校验
	if req.ConvId == "" {
		return nil, errors.New("会话ID不能为空")
	}

	// 2. 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 || req.Size > 100 {
		req.Size = 100 // 默认每页100条消息
	}

	// 3. 解析会话ID，确定会话类型和目标ID
	var convType int8
	var targetId string

	if strings.Contains(req.ConvId, "-") {
		// 3.1 单聊会话
		convType = consts.ChatMsgType_Private
		parts := strings.Split(req.ConvId, "-")
		if len(parts) != 2 {
			return nil, errors.New("无效的会话ID格式")
		}

		// 确定目标ID（对方ID）
		if parts[0] == userId {
			targetId = parts[1]
		} else if parts[1] == userId {
			targetId = parts[0]
		} else {
			return nil, errors.New("您不是该会话的成员")
		}

		// 验证是否是好友关系
		var friendCount int64
		result := dao.Db.Model(&modelChat.FriendRelation{}).
			Where("user_id = ? AND friend_id = ? AND deleted_at IS NULL", userId, targetId).
			Count(&friendCount)

		if result.Error != nil {
			return nil, fmt.Errorf("验证好友关系失败: %w", result.Error)
		}

		if friendCount == 0 {
			return nil, errors.New("对方不是您的好友，无法查看消息")
		}
	} else {
		// 3.2 群聊会话
		convType = consts.ChatMsgType_Group
		targetId = req.ConvId

		// 检查用户是否是群成员
		var memberCount int64
		result := dao.Db.Model(&modelChat.ChatGroupMember{}).
			Where("group_id = ? AND user_id = ? AND deleted_at IS NULL AND exit_time IS NULL", targetId, userId).
			Count(&memberCount)

		if result.Error != nil {
			return nil, fmt.Errorf("验证群成员关系失败: %w", result.Error)
		}

		if memberCount == 0 {
			return nil, errors.New("您不是该群组的成员，无法查看消息")
		}
	}

	// 4. 构建查询 - 使用联表查询一次性获取所有需要的数据

	var messagesWithReceiver []MessageWithReceiver
	var queryBuilder *gorm.DB

	if convType == consts.ChatMsgType_Private {
		// 4.1 单聊：查询双方之间的消息（修复接收状态查询逻辑）
		queryBuilder = dao.Db.Table("chat_messages as cm").
			Select(`
				cm.msg_id, cm.msg_type, cm.sender_id, cm.send_time,
				cm.msg_content_fmt, cm.msg_content_type, cm.msg_content,
				cmr.status as receiver_status, cmr.read_time
			`).
			Joins(`LEFT JOIN chat_message_receivers as cmr ON cm.msg_id = cmr.msg_id
				AND cmr.receiver_id = CASE
					WHEN cm.sender_id = ? THEN cm.receiver_id
					ELSE ?
				END`, userId, userId).
			Where("((cm.sender_id = ? AND cm.receiver_id = ?) OR (cm.sender_id = ? AND cm.receiver_id = ?)) AND cm.deleted_at IS NULL",
				userId, targetId, targetId, userId)
	} else {
		// 4.2 群聊：查询群内的消息
		queryBuilder = dao.Db.Table("chat_messages as cm").
			Select(`
				cm.msg_id, cm.msg_type, cm.sender_id, cm.send_time,
				cm.msg_content_fmt, cm.msg_content_type, cm.msg_content,
				cmr.status as receiver_status, cmr.read_time
			`).
			Joins("LEFT JOIN chat_message_receivers as cmr ON cm.msg_id = cmr.msg_id AND cmr.receiver_id = ?", userId).
			Where("cm.receiver_id = ? AND cm.receiver_type = ? AND cm.deleted_at IS NULL",
				targetId, convType)
	}

	// 5. 获取消息总数
	var totalCount int64
	countQuery := queryBuilder.Session(&gorm.Session{})
	result := countQuery.Count(&totalCount)
	if result.Error != nil {
		return nil, fmt.Errorf("获取消息总数失败: %w", result.Error)
	}

	// 6. 获取消息列表
	result = queryBuilder.Order("cm.send_time DESC"). // 按发送时间降序
								Offset((req.Page - 1) * req.Size).
								Limit(req.Size).
								Find(&messagesWithReceiver)

	if result.Error != nil {
		return nil, fmt.Errorf("获取消息列表失败: %w", result.Error)
	}

	// 7. 收集所有发送者ID，用于批量获取用户信息
	senderIds := make([]string, 0, len(messagesWithReceiver))
	senderIdMap := make(map[string]bool)

	for _, msg := range messagesWithReceiver {
		if !senderIdMap[msg.SenderId] {
			senderIds = append(senderIds, msg.SenderId)
			senderIdMap[msg.SenderId] = true
		}
	}

	// 8. 批量获取用户信息
	var userInfos []user.UserInfoRes
	if len(senderIds) > 0 {
		dao.Db.Model(&modelUser.UserInfo{}).
			Select("user_id, user_nick, user_avatar").
			Where("user_id IN ?", senderIds).
			Find(&userInfos)
	}

	// 创建用户信息映射
	userInfoMap := make(map[string]user.UserInfoRes, len(userInfos))
	for _, userIfno := range userInfos {
		userInfoMap[userIfno.UserId] = userIfno
	}

	// 9. 获取群聊消息的接收者状态统计（仅群聊需要）
	var msgReceiversMap map[string][]chat.MessageReceiverInfo
	if convType == consts.ChatMsgType_Group {
		msgReceiversMap = s.getGroupMessageReceiversStatus(messagesWithReceiver, targetId)
	}

	// 10. 构建响应数据
	msgList := make([]chat.MessageInfo, 0, len(messagesWithReceiver))

	for _, msg := range messagesWithReceiver {
		// 获取群聊接收者状态（仅群聊需要）
		var receivers []chat.MessageReceiverInfo
		if convType == consts.ChatMsgType_Group && msgReceiversMap != nil {
			receivers = msgReceiversMap[msg.MsgId]
		}

		// 构建消息信息（使用共用函数）
		messageInfo := s.buildMessageInfo(
			msg.MsgId,
			msg.MsgType,
			msg.SenderId,
			msg.SendTime,
			msg.MsgContentFmt,
			msg.MsgContentType,
			msg.MsgContent,
			msg.ReceiverStatus,
			msg.ReadTime,
			req.ConvId,
			convType,
			userId,
			userInfoMap,
			receivers,
		)

		msgList = append(msgList, messageInfo)
	}

	// 10. 异步更新会话未读数为0
	/*go func() {
		dao.Db.Model(&modelChat.ChatConversation{}).
			Where("user_id = ? AND target_id = ? AND conv_type = ?", userId, targetId, convType).
			Update("unread_count", 0)
	}()*/

	// 11. 返回结果
	res = &chat.GetMsgListForConvIdRes{
		List:    msgList,
		Count:   int(totalCount),
		HasMore: int(totalCount) > (req.Page * req.Size),
	}

	return res, nil
}

// MarkConvMsgRead 3、会话消息-标记已读(部分消息)
func (s *ServerMessage) MarkConvMsgRead(req *chat.MarkConvMsgReadReq, userId string) (res *chat.MarkConvMsgReadRes, err error) {
	// 参数校验
	if req.ConvId == "" {
		return nil, errors.New("会话ID不能为空")
	}
	if len(req.MsgIds) == 0 {
		return nil, errors.New("消息ID列表不能为空")
	}

	// 解析会话ID，确定会话类型和目标ID
	var convType int
	var targetId string

	if strings.Contains(req.ConvId, "-") {
		// 单聊会话
		convType = consts.ChatMsgType_Private
		parts := strings.Split(req.ConvId, "-")
		if len(parts) != 2 {
			return nil, errors.New("无效的会话ID格式")
		}

		// 确定目标ID（对方ID）
		if parts[0] == userId {
			targetId = parts[1]
		} else if parts[1] == userId {
			targetId = parts[0]
		} else {
			return nil, errors.New("您不是该会话的成员")
		}
	} else {
		// 群聊会话
		convType = consts.ChatMsgType_Group
		targetId = req.ConvId
	}

	// 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	// 验证消息是否属于该会话，并且只允许标记别人发送的消息为已读
	var validMsgIds []string
	var query *gorm.DB
	if convType == consts.ChatMsgType_Private {
		// 单聊：只允许标记对方发送给自己的消息为已读
		query = tx.Model(&modelChat.ChatMessage{}).
			Select("msg_id").
			Where(
				"msg_id IN ? AND sender_id = ? AND receiver_id = ? AND deleted_at IS NULL",
				req.MsgIds, targetId, userId,
			)
	} else {
		// 群聊：只允许标记别人发送到群里的消息为已读（排除自己发送的消息）
		query = tx.Model(&modelChat.ChatMessage{}).
			Select("msg_id").
			Where(
				"msg_id IN ? AND receiver_id = ? AND receiver_type = ? AND sender_id != ? AND deleted_at IS NULL",
				req.MsgIds, targetId, convType, userId,
			)
	}

	result := query.Pluck("msg_id", &validMsgIds)
	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 如果没有有效消息，直接返回
	if len(validMsgIds) == 0 {
		tx.Rollback()
		return &chat.MarkConvMsgReadRes{
			SuccessCount: 0,
		}, nil
	}

	// 更新消息接收状态
	now := time.Now()
	readTime := gtime.Now()
	result = tx.Model(&modelChat.ChatMessageReceiver{}).Where(
		"msg_id IN ? AND receiver_id = ? AND status < ?",
		validMsgIds, userId, consts.MessageStatus_Readed,
	).Updates(map[string]interface{}{
		"read_time":  readTime,
		"status":     consts.MessageStatus_Readed,
		"updated_at": now,
	})

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 获取更新的记录数
	var updatedCount int64 = result.RowsAffected

	// 如果有消息被标记为已读，更新会话的未读计数
	if updatedCount > 0 {
		// 直接更新会话的未读计数，使用SQL的GREATEST函数确保不会出现负数
		// 这样可以避免先查询再更新的两步操作，提高性能
		result = tx.Exec(
			"UPDATE chat_conversations SET unread_count = GREATEST(0, unread_count - ?), updated_at = ? WHERE user_id = ? AND target_id = ? AND conv_type = ?",
			updatedCount, now, userId, targetId, convType,
		)

		if result.Error != nil {
			tx.Rollback()
			return nil, result.Error
		}
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		return nil, err
	}

	// 返回结果
	res = &chat.MarkConvMsgReadRes{
		SuccessCount: int(updatedCount),
	}

	return
}

// MarkConvAllMsgRead 4、会话消息-标记已读(全部)
func (s *ServerMessage) MarkConvAllMsgRead(req *chat.MarkConvAllMsgReadReq, userId string) (res *chat.MarkConvAllMsgReadRes, err error) {
	if req.ConvId == "" {
		// 如果会话ID为空，标记所有会话的所有消息为已读
		return s.markAllConvAllMsgRead(req, userId)
	} else {
		// 如果会话ID不为空，标记指定会话的所有消息为已读
		return s.markConvAllMsgRead(req, userId)
	}
}

// markConvAllMsgRead 4.1 标记指定会话所有消息已读
func (s *ServerMessage) markConvAllMsgRead(req *chat.MarkConvAllMsgReadReq, userId string) (res *chat.MarkConvAllMsgReadRes, err error) {
	// 1. 参数校验
	if req.ConvId == "" {
		return nil, errors.New("会话ID不能为空")
	}

	// 2. 解析会话ID，确定会话类型和目标ID
	var convType int
	var targetId string

	if strings.Contains(req.ConvId, "-") {
		// 2.1 单聊会话
		convType = consts.ChatMsgType_Private
		parts := strings.Split(req.ConvId, "-")
		if len(parts) != 2 {
			return nil, errors.New("无效的会话ID格式")
		}

		// 确定目标ID（对方ID）
		if parts[0] == userId {
			targetId = parts[1]
		} else if parts[1] == userId {
			targetId = parts[0]
		} else {
			return nil, errors.New("您不是该会话的成员")
		}

		// 验证是否是好友关系
		var friendCount int64
		result := dao.Db.Model(&modelChat.FriendRelation{}).
			Where("user_id = ? AND friend_id = ? AND deleted_at IS NULL", userId, targetId).
			Count(&friendCount)

		if result.Error != nil {
			return nil, fmt.Errorf("验证好友关系失败: %w", result.Error)
		}

		if friendCount == 0 {
			return nil, errors.New("对方不是您的好友，无法标记消息已读")
		}
	} else {
		// 2.2 群聊会话
		convType = consts.ChatMsgType_Group
		targetId = req.ConvId

		// 检查用户是否是群成员
		var memberCount int64
		result := dao.Db.Model(&modelChat.ChatGroupMember{}).
			Where("group_id = ? AND user_id = ? AND deleted_at IS NULL", targetId, userId).
			Count(&memberCount)

		if result.Error != nil {
			return nil, fmt.Errorf("验证群成员关系失败: %w", result.Error)
		}

		if memberCount == 0 {
			return nil, errors.New("您不是该群组的成员，无法标记消息已读")
		}
	}

	// 3. 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		} else if err != nil {
			tx.Rollback()
		}
	}()

	// 4. 查询需要标记为已读的消息ID
	var unreadMsgIds []string
	var query *gorm.DB

	if convType == consts.ChatMsgType_Private {
		// 4.1 单聊：查询对方发送给自己且未读的消息
		query = tx.Model(&modelChat.ChatMessage{}).
			Select("cm.msg_id").
			Joins("LEFT JOIN chat_message_receivers as cmr ON cm.msg_id = cmr.msg_id AND cmr.receiver_id = ?", userId).
			Where("cm.sender_id = ? AND cm.receiver_id = ? AND cm.deleted_at IS NULL AND (cmr.status IS NULL OR cmr.status < ?)",
				targetId, userId, consts.MessageStatus_Readed)
	} else {
		// 4.2 群聊：查询别人发送到群里且自己未读的消息（排除自己发送的消息）
		query = tx.Model(&modelChat.ChatMessage{}).
			Select("cm.msg_id").
			Joins("LEFT JOIN chat_message_receivers as cmr ON cm.msg_id = cmr.msg_id AND cmr.receiver_id = ?", userId).
			Where("cm.receiver_id = ? AND cm.receiver_type = ? AND cm.sender_id != ? AND cm.deleted_at IS NULL AND (cmr.status IS NULL OR cmr.status < ?)",
				targetId, convType, userId, consts.MessageStatus_Readed)
	}

	result := query.Table("chat_messages as cm").Pluck("cm.msg_id", &unreadMsgIds)
	if result.Error != nil {
		return nil, fmt.Errorf("查询未读消息失败: %w", result.Error)
	}

	// 5. 如果没有未读消息，直接返回
	if len(unreadMsgIds) == 0 {
		tx.Commit() // 提交空事务
		return &chat.MarkConvAllMsgReadRes{
			SuccessCount: 0,
		}, nil
	}

	// 6. 更新消息接收状态
	now := time.Now()
	readTime := gtime.Now()

	// 6.1 查找已有的接收记录
	var existingReceiverIds []string
	result = tx.Model(&modelChat.ChatMessageReceiver{}).
		Where("msg_id IN ? AND receiver_id = ?", unreadMsgIds, userId).
		Pluck("msg_id", &existingReceiverIds)

	if result.Error != nil {
		return nil, fmt.Errorf("查询现有接收记录失败: %w", result.Error)
	}

	// 6.2 创建接收记录ID映射，用于快速查找
	existingMap := make(map[string]bool, len(existingReceiverIds))
	for _, id := range existingReceiverIds {
		existingMap[id] = true
	}

	// 6.3 更新已有的接收记录
	if len(existingReceiverIds) > 0 {
		result = tx.Model(&modelChat.ChatMessageReceiver{}).
			Where("msg_id IN ? AND receiver_id = ?", existingReceiverIds, userId).
			Updates(map[string]interface{}{
				"read_time":  readTime,
				"status":     consts.MessageStatus_Readed,
				"updated_at": now,
			})

		if result.Error != nil {
			return nil, fmt.Errorf("更新消息接收状态失败: %w", result.Error)
		}
	}

	// 6.4 创建新的接收记录
	var newReceivers []modelChat.ChatMessageReceiver
	for _, msgId := range unreadMsgIds {
		if !existingMap[msgId] {
			newReceivers = append(newReceivers, modelChat.ChatMessageReceiver{
				ConvId:     req.ConvId,
				MsgId:      msgId,
				ReceiverId: userId,
				ReadTime:   readTime,
				Status:     consts.MessageStatus_Readed,
				CreatedAt:  &now,
				UpdatedAt:  &now,
			})
		}
	}

	// 批量创建新的接收记录
	if len(newReceivers) > 0 {
		result = tx.Create(&newReceivers)
		if result.Error != nil {
			return nil, fmt.Errorf("创建消息接收记录失败: %w", result.Error)
		}
	}

	// 7. 更新会话的未读计数为0
	result = tx.Model(&modelChat.ChatConversation{}).
		Where("user_id = ? AND target_id = ? AND conv_type = ?", userId, targetId, convType).
		Updates(map[string]interface{}{
			"unread_count": 0,
			"updated_at":   now,
		})

	if result.Error != nil {
		return nil, fmt.Errorf("更新会话未读计数失败: %w", result.Error)
	}

	// 8. 提交事务
	if err = tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	// 9. 返回结果
	return &chat.MarkConvAllMsgReadRes{
		SuccessCount: len(unreadMsgIds),
	}, nil
}

// markAllConvAllMsgRead 4.2 标记所有会话的所有消息为已读
func (s *ServerMessage) markAllConvAllMsgRead(req *chat.MarkConvAllMsgReadReq, userId string) (res *chat.MarkConvAllMsgReadRes, err error) {
	// 1. 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		} else if err != nil {
			tx.Rollback()
		}
	}()

	// 2. 获取用户的所有会话
	var conversations []modelChat.ChatConversation
	result := tx.Model(&modelChat.ChatConversation{}).
		Where("user_id = ? AND deleted_at IS NULL AND unread_count > 0", userId).
		Find(&conversations)

	if result.Error != nil {
		return nil, fmt.Errorf("获取用户会话列表失败: %w", result.Error)
	}

	// 3. 如果没有会话，直接返回
	if len(conversations) == 0 {
		tx.Commit() // 提交空事务
		return &chat.MarkConvAllMsgReadRes{
			SuccessCount: 0,
		}, nil
	}

	// 4. 收集所有会话的目标ID和类型，用于后续查询
	type ConvTarget struct {
		TargetId string
		ConvType int8
		ConvId   string
	}

	convTargets := make([]ConvTarget, 0, len(conversations))
	for _, conv := range conversations {
		convTargets = append(convTargets, ConvTarget{
			TargetId: conv.TargetId,
			ConvType: conv.ConvType,
			ConvId:   conv.ConvId,
		})
	}

	// 5. 查询所有未读消息
	var totalUnreadMsgIds []string
	now := time.Now()
	readTime := gtime.Now()
	totalMarkedCount := 0

	// 6. 逐个处理每个会话
	for _, target := range convTargets {
		var unreadMsgIds []string
		var query *gorm.DB

		if target.ConvType == consts.ChatMsgType_Private {
			// 6.1 单聊：查询对方发送给自己且未读的消息
			query = tx.Model(&modelChat.ChatMessage{}).
				Select("cm.msg_id").
				Table("chat_messages as cm").
				Joins("LEFT JOIN chat_message_receivers as cmr ON cm.msg_id = cmr.msg_id AND cmr.receiver_id = ?", userId).
				Where("cm.sender_id = ? AND cm.receiver_id = ? AND cm.deleted_at IS NULL AND (cmr.status IS NULL OR cmr.status < ?)",
					target.TargetId, userId, consts.MessageStatus_Readed)
		} else {
			// 6.2 群聊：查询别人发送到群里且自己未读的消息（排除自己发送的消息）
			query = tx.Model(&modelChat.ChatMessage{}).
				Select("cm.msg_id").
				Table("chat_messages as cm").
				Joins("LEFT JOIN chat_message_receivers as cmr ON cm.msg_id = cmr.msg_id AND cmr.receiver_id = ?", userId).
				Where("cm.receiver_id = ? AND cm.receiver_type = ? AND cm.sender_id != ? AND cm.deleted_at IS NULL AND (cmr.status IS NULL OR cmr.status < ?)",
					target.TargetId, target.ConvType, userId, consts.MessageStatus_Readed)
		}

		result = query.Pluck("cm.msg_id", &unreadMsgIds)
		if result.Error != nil {
			return nil, fmt.Errorf("查询会话 %s 的未读消息失败: %w", target.ConvId, result.Error)
		}

		if len(unreadMsgIds) == 0 {
			continue // 跳过没有未读消息的会话
		}

		totalUnreadMsgIds = append(totalUnreadMsgIds, unreadMsgIds...)

		// 6.3 更新会话的未读计数为0
		result = tx.Model(&modelChat.ChatConversation{}).
			Where("user_id = ? AND target_id = ? AND conv_type = ?", userId, target.TargetId, target.ConvType).
			Updates(map[string]interface{}{
				"unread_count": 0,
				"updated_at":   now,
			})

		if result.Error != nil {
			return nil, fmt.Errorf("更新会话 %s 的未读计数失败: %w", target.ConvId, result.Error)
		}
	}

	// 7. 如果没有未读消息，直接返回
	if len(totalUnreadMsgIds) == 0 {
		tx.Commit() // 提交事务
		return &chat.MarkConvAllMsgReadRes{
			SuccessCount: 0,
		}, nil
	}

	// 8. 查找已有的接收记录
	var existingReceiverIds []string
	result = tx.Model(&modelChat.ChatMessageReceiver{}).
		Where("msg_id IN ? AND receiver_id = ?", totalUnreadMsgIds, userId).
		Pluck("msg_id", &existingReceiverIds)

	if result.Error != nil {
		return nil, fmt.Errorf("查询现有接收记录失败: %w", result.Error)
	}

	// 8.1 创建接收记录ID映射，用于快速查找
	existingMap := make(map[string]bool, len(existingReceiverIds))
	for _, id := range existingReceiverIds {
		existingMap[id] = true
	}

	// 8.2 更新已有的接收记录
	if len(existingReceiverIds) > 0 {
		result = tx.Model(&modelChat.ChatMessageReceiver{}).
			Where("msg_id IN ? AND receiver_id = ?", existingReceiverIds, userId).
			Updates(map[string]interface{}{
				"read_time":  readTime,
				"status":     consts.MessageStatus_Readed,
				"updated_at": now,
			})

		if result.Error != nil {
			return nil, fmt.Errorf("更新消息接收状态失败: %w", result.Error)
		}

		totalMarkedCount += len(existingReceiverIds)
	}

	// 8.3 创建新的接收记录
	// 为每个消息找到对应的会话ID
	msgToConvMap := make(map[string]string)
	for _, target := range convTargets {
		var msgIds []string

		if target.ConvType == consts.ChatMsgType_Private {
			// 单聊
			result = tx.Model(&modelChat.ChatMessage{}).
				Where("((sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?)) AND msg_id IN ? AND deleted_at IS NULL",
					userId, target.TargetId, target.TargetId, userId, totalUnreadMsgIds).
				Pluck("msg_id", &msgIds)
		} else {
			// 群聊
			result = tx.Model(&modelChat.ChatMessage{}).
				Where("receiver_id = ? AND receiver_type = ? AND msg_id IN ? AND deleted_at IS NULL",
					target.TargetId, target.ConvType, totalUnreadMsgIds).
				Pluck("msg_id", &msgIds)
		}

		if result.Error != nil {
			return nil, fmt.Errorf("查询会话 %s 的消息失败: %w", target.ConvId, result.Error)
		}

		for _, msgId := range msgIds {
			msgToConvMap[msgId] = target.ConvId
		}
	}

	// 创建新的接收记录
	var newReceivers []modelChat.ChatMessageReceiver
	for _, msgId := range totalUnreadMsgIds {
		if !existingMap[msgId] {
			convId, exists := msgToConvMap[msgId]
			if !exists {
				// 如果找不到对应的会话ID，跳过
				continue
			}

			newReceivers = append(newReceivers, modelChat.ChatMessageReceiver{
				ConvId:     convId,
				MsgId:      msgId,
				ReceiverId: userId,
				ReadTime:   readTime,
				Status:     consts.MessageStatus_Readed,
				CreatedAt:  &now,
				UpdatedAt:  &now,
			})
		}
	}

	// 批量创建新的接收记录
	if len(newReceivers) > 0 {
		result = tx.Create(&newReceivers)
		if result.Error != nil {
			return nil, fmt.Errorf("创建消息接收记录失败: %w", result.Error)
		}

		totalMarkedCount += len(newReceivers)
	}

	// 9. 提交事务
	if err = tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	// 10. 返回结果
	return &chat.MarkConvAllMsgReadRes{
		SuccessCount: totalMarkedCount,
	}, nil
}

// GetUnreadCount 5、未读消息数-获取
func (s *ServerMessage) GetUnreadCount(req *chat.GetUnreadCountReq, userId string) (res *chat.GetUnreadCountRes, err error) {
	// 构建查询条件
	query := dao.Db.Model(&modelChat.ChatConversation{}).Where(
		"user_id = ? AND deleted_at IS NULL AND unread_count > 0",
		userId,
	)

	// 如果指定了会话ID，则只查询该会话
	if req.ConvId != "" {
		query = query.Where("conv_id = ?", req.ConvId)
	}

	// 如果指定了目标类型，则按类型筛选
	if req.ConvType > 0 {
		query = query.Where("conv_type = ?", req.ConvType)
	}

	// 获取未读消息总数
	var totalUnreadCount int64
	result := query.Select("COALESCE(SUM(unread_count), 0)").Scan(&totalUnreadCount)
	if result.Error != nil {
		return nil, result.Error
	}

	// 获取未读消息列表 - 使用全新的DB实例，避免受到前面Select语句的影响
	listQuery := dao.Db.Session(&gorm.Session{}).Model(&modelChat.ChatConversation{}).Where(
		"user_id = ? AND deleted_at IS NULL AND unread_count > 0",
		userId,
	)

	// 应用相同的过滤条件
	if req.ConvId != "" {
		listQuery = listQuery.Where("conv_id = ?", req.ConvId)
	}
	if req.ConvType > 0 {
		listQuery = listQuery.Where("conv_type = ?", req.ConvType)
	}

	// 执行查询获取会话列表
	var conversations []modelChat.ChatConversation
	result = listQuery.Find(&conversations)
	if result.Error != nil {
		return nil, result.Error
	}

	// 构建响应数据
	var unreadList []chat.UnreadCountInfo
	for _, conv := range conversations {
		unreadInfo := chat.UnreadCountInfo{
			ConvId:      conv.ConvId,
			ConvType:    conv.ConvType,
			UnreadCount: conv.UnreadCount,
			LastMsgId:   conv.LastMsgId,
			LastMsgTime: tools.GtimeToStringNMDHMS(conv.LastMsgTime),
		}
		unreadList = append(unreadList, unreadInfo)
	}

	// 返回结果
	res = &chat.GetUnreadCountRes{
		List:       unreadList,
		TotalCount: int(totalUnreadCount),
	}

	return
}

// 更新消息体状态
func (s *ServerMessage) updateMessageStatus(msgId string, receiverId string, status int) {
	// 更新消息接收状态
	dao.Db.Model(&modelChat.ChatMessageReceiver{}).Where(
		"msg_id = ? AND receiver_id = ?",
		msgId, receiverId,
	).Update("status", status)
}

// 计算分片键 - 用于分表策略
func (s *ServerMessage) calculateShardKey(senderId string, receiverId string) int8 {
	// 使用接收者ID的哈希值作为分片键
	// 如果是群聊，直接使用群ID
	// 如果是单聊，使用两个用户ID的组合
	var key string
	if receiverId != "" {
		key = receiverId
	} else {
		key = senderId
	}

	// 简单的哈希算法，将字符串转为整数后对分片数取模
	hashValue := int8(0)
	for _, c := range key {
		hashValue = hashValue*31 + int8(c)
	}

	// 确保返回正数
	if hashValue < 0 {
		hashValue = -hashValue
	}

	return hashValue % s.shardCount
}

// 生成会话ID(单聊 与 群聊, 客户端也与客户端生成的会话id 一致)
func (s *ServerMessage) generateConvId(convType int8, userId string, targetId string) string {
	if convType == consts.ChatMsgType_Private {
		// 单聊：确保两个用户ID按字典序排序，保证生成的会话ID唯一
		if userId < targetId {
			return userId + "-" + targetId
		} else {
			return targetId + "-" + userId
		}
	} else {
		// 群聊：直接使用群ID作为会话ID
		return targetId
	}
}

// 保存单聊消息到数据库
func (s *ServerMessage) savePrivateMessageToDatabase(msg *MessageItem) {
	// 计算分片键
	shardKey := s.calculateShardKey(msg.SenderId, msg.ReceiverId)

	// 创建消息记录
	now := time.Now()
	sendTime := msg.SendTime
	if sendTime == nil {
		sendTime = gtime.Now()
	}

	// 将消息内容转换为字符串
	var msgContentStr string
	if contentBytes, err := json.Marshal(msg.MsgContent); err == nil {
		msgContentStr = string(contentBytes)
	} else {
		msgContentStr = fmt.Sprintf("%v", msg.MsgContent)
	}

	// 创建消息记录
	message := &modelChat.ChatMessage{
		MsgType:        msg.MsgType,
		MsgId:          msg.MsgId,
		Status:         consts.MessageStatus_Sended, // 已发送
		SenderId:       msg.SenderId,
		SendTime:       sendTime,
		ReceiverId:     msg.ReceiverId,
		ReceiverType:   msg.ReceiverType, // 1表示单聊
		MsgContentFmt:  msg.MsgContentFmt,
		MsgContentType: msg.MsgContentType,
		MsgContent:     msgContentStr,
		ShardKey:       shardKey,
		CreatedAt:      &now,
		UpdatedAt:      &now,
	}

	// 1、保存发送的消息体
	dao.Db.Create(message)

	// 创建消息接收记录
	receiver := &modelChat.ChatMessageReceiver{
		ConvId:     msg.ConvId,
		MsgId:      msg.MsgId,
		ReceiverId: msg.ReceiverId,
		Status:     consts.MessageStatus_Sending, // 对应接收者 是发送中
		ShardKey:   s.calculateShardKey("", msg.ReceiverId),
		CreatedAt:  &now,
		UpdatedAt:  &now,
	}

	// 2、保存接收者的消息记录
	result := dao.Db.Create(receiver)
	if result.Error != nil {
		g.Log().Errorf(context.Background(), "保存接收者的消息记录失败 :%v", result)
	}

	// 3、更新或创建会话
	s.updateConversation(consts.ChatMsgType_Private, msg.SenderId, msg.ReceiverId, msg.MsgId, sendTime)
	s.updateConversation(consts.ChatMsgType_Private, msg.ReceiverId, msg.SenderId, msg.MsgId, sendTime)
}

// 保存群聊消息到数据库
func (s *ServerMessage) saveGroupMessageToDatabase(msg *MessageItem, members []string) {
	// 计算分片键
	shardKey := s.calculateShardKey(msg.SenderId, msg.ReceiverId)

	// 创建消息记录
	now := time.Now()
	sendTime := msg.SendTime
	if sendTime == nil {
		sendTime = gtime.Now()
	}

	// 将消息内容转换为字符串
	var msgContentStr string
	if contentBytes, err := json.Marshal(msg.MsgContent); err == nil {
		msgContentStr = string(contentBytes)
	} else {
		msgContentStr = fmt.Sprintf("%v", msg.MsgContent)
	}

	// 1、创建消息记录
	message := &modelChat.ChatMessage{
		MsgType:        msg.MsgType,
		MsgId:          msg.MsgId,
		Status:         consts.MessageStatus_Sended, // 已发送
		SenderId:       msg.SenderId,
		SendTime:       sendTime,
		ReceiverId:     msg.ReceiverId,   // 群聊的ReceiverId就是群ID
		ReceiverType:   msg.ReceiverType, // 2表示群聊
		MsgContentFmt:  msg.MsgContentFmt,
		MsgContentType: msg.MsgContentType,
		MsgContent:     msgContentStr,
		ShardKey:       shardKey,
		CreatedAt:      &now,
		UpdatedAt:      &now,
	}

	// 保存消息
	dao.Db.Create(message)

	// 为每个成员创建消息接收记录
	for _, memberUserId := range members {
		// 跳过发送者自己
		/*if memberUserId == msg.SenderId {
			continue
		}*/

		receiver := &modelChat.ChatMessageReceiver{
			MsgId:      msg.MsgId,
			ReceiverId: memberUserId,
			Status:     message.Status, // 已发送
			ShardKey:   s.calculateShardKey("", memberUserId),
			CreatedAt:  &now,
			UpdatedAt:  &now,
		}

		// 1、保存接收记录
		dao.Db.Create(receiver)

		// 2、更新成员的会话(包含了自己与其他成员)
		s.updateConversation(consts.ChatMsgType_Group, memberUserId, msg.ReceiverId, msg.MsgId, sendTime)
	}
	// 更新发送者的会话
	//s.updateConversation(consts.ChatMsgType_Group, msg.SenderId, msg.ReceiverId, msg.MsgId, msgContentStr, sendTime)
}

// 更新会话
func (s *ServerMessage) updateConversation(ConvType int8, sendUserId string, targetId string, msgId string, msgTime *gtime.Time) {
	// 生成会话ID
	convId := s.generateConvId(ConvType, sendUserId, targetId)

	// 查找现有会话
	var conversation modelChat.ChatConversation
	result := dao.Db.Model(&modelChat.ChatConversation{}).Where(
		"user_id = ? AND target_id = ? AND conv_type = ?",
		sendUserId, targetId, ConvType,
	).First(&conversation)

	now := time.Now()

	// 如果会话不存在，创建新会话
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// 创建新会话
			newConv := &modelChat.ChatConversation{
				ConvId:      convId,
				ConvType:    ConvType,
				UserId:      sendUserId,
				TargetId:    targetId,
				LastMsgId:   msgId,
				LastMsgTime: msgTime,
				UnreadCount: 0,
				Status:      consts.ChatConvStatus_Normal, // 正常
				CreatedAt:   &now,
				UpdatedAt:   &now,
			}

			// 如果是接收者，设置未读数为1
			if sendUserId != targetId {
				newConv.UnreadCount = 1
			}

			// 保存新会话
			result = dao.Db.Create(newConv)
			if result.Error != nil {
				g.Log().Errorf(context.Background(), "创建会话失败 :%v", result)
			}
		}
	} else {
		// 更新现有会话
		updateData := map[string]interface{}{
			"last_msg_id":   msgId,
			"last_msg_time": msgTime,
			"updated_at":    &now,
		}

		// 如果是接收者，增加未读数
		if sendUserId != conversation.UserId {
			updateData["unread_count"] = gorm.Expr("unread_count + 1")
		}

		// 更新会话
		dao.Db.Model(&conversation).Updates(updateData)
	}
}

// 获取用户基本信息
func (s *ServerMessage) getUserInfo(userId string) (userName string, userAvatar string) {
	// 这里应该调用用户服务获取用户信息
	// 为简化实现，这里直接从数据库查询
	var userInfo struct {
		UserName   string
		UserAvatar string
	}

	// 查询用户信息
	dao.Db.Table("user_info").Select("user_name, user_avatar").Where("user_id = ?", userId).Scan(&userInfo)

	return userInfo.UserName, userInfo.UserAvatar
}

// ConvInfoMaps 会话信息映射结构体
type ConvInfoMaps struct {
	UserInfoMap     map[string]user.UserInfoRes
	GroupInfoMap    map[string]GroupWithMemberInfo
	FriendRemarkMap map[string]string // 好友备注映射，key为好友ID，value为备注
}

// GroupWithMemberInfo 群组信息和成员备注
type GroupWithMemberInfo struct {
	GroupId     string `gorm:"column:group_id"`
	GroupName   string `gorm:"column:group_name"`
	GroupAvatar string `gorm:"column:group_avatar"`
	Remarks     string `gorm:"column:remarks"`
}

// getConvNickAndAvatar 获取会话昵称和头像信息
func (s *ServerMessage) getConvNickAndAvatar(conversations []ConversationWithLastMsg, userId string) *ConvInfoMaps {
	// 1. 收集所有需要查询的ID
	senderIds := make([]string, 0, len(conversations))
	targetUserIds := make([]string, 0, len(conversations))
	groupIds := make([]string, 0, len(conversations))

	senderIdMap := make(map[string]bool)
	targetUserIdMap := make(map[string]bool)
	groupIdMap := make(map[string]bool)

	for _, conv := range conversations {
		// 收集最后一条消息的发送者ID
		if conv.LastMsgSenderId != "" && !senderIdMap[conv.LastMsgSenderId] {
			senderIds = append(senderIds, conv.LastMsgSenderId)
			senderIdMap[conv.LastMsgSenderId] = true
		}

		// 收集会话相关的ID
		if conv.ConvType == consts.ChatMsgType_Private {
			// 单聊：收集目标用户ID
			if !targetUserIdMap[conv.TargetId] {
				targetUserIds = append(targetUserIds, conv.TargetId)
				targetUserIdMap[conv.TargetId] = true
			}
		} else if conv.ConvType == consts.ChatMsgType_Group {
			// 群聊：收集群组ID
			if !groupIdMap[conv.TargetId] {
				groupIds = append(groupIds, conv.TargetId)
				groupIdMap[conv.TargetId] = true
			}
		}
	}

	// 2. 批量获取用户信息
	allUserIds := make([]string, 0, len(senderIds)+len(targetUserIds))
	allUserIds = append(allUserIds, senderIds...)
	allUserIds = append(allUserIds, targetUserIds...)

	// 去重
	uniqueUserIds := make([]string, 0, len(allUserIds))
	uniqueUserIdMap := make(map[string]bool)
	for _, id := range allUserIds {
		if !uniqueUserIdMap[id] {
			uniqueUserIds = append(uniqueUserIds, id)
			uniqueUserIdMap[id] = true
		}
	}

	var userInfos []user.UserInfoRes
	if len(uniqueUserIds) > 0 {
		dao.Db.Model(&modelUser.UserInfo{}).
			Select("user_id, user_nick, user_avatar").
			Where("user_id IN ?", uniqueUserIds).
			Find(&userInfos)
	}

	// 创建用户信息映射
	userInfoMap := make(map[string]user.UserInfoRes, len(userInfos))
	for _, userInfo := range userInfos {
		userInfoMap[userInfo.UserId] = userInfo
	}

	// 3. 批量获取好友备注信息（仅针对单聊的目标用户）
	type FriendRemarkInfo struct {
		FriendId string `gorm:"column:friend_id"`
		Remark   string `gorm:"column:remark"`
	}

	var friendRemarks []FriendRemarkInfo
	friendRemarkMap := make(map[string]string)

	if len(targetUserIds) > 0 {
		dao.Db.Model(&modelChat.FriendRelation{}).
			Select("friend_id, remark").
			Where("user_id = ? AND friend_id IN ? AND deleted_at IS NULL AND remark != ''", userId, targetUserIds).
			Find(&friendRemarks)

		// 创建好友备注映射
		for _, remark := range friendRemarks {
			friendRemarkMap[remark.FriendId] = remark.Remark
		}
	}

	// 3. 批量获取群组信息和用户在群组中的备注
	var groupInfos []GroupWithMemberInfo
	if len(groupIds) > 0 {
		dao.Db.Table("chat_group_infos as g").
			Select("g.group_id, g.group_name, g.group_avatar, m.remarks").
			Joins("LEFT JOIN chat_group_members as m ON g.group_id = m.group_id AND m.user_id = ? AND m.deleted_at IS NULL", userId).
			Where("g.group_id IN ? AND g.deleted_at IS NULL", groupIds).
			Find(&groupInfos)
	}

	// 创建群组信息映射
	groupInfoMap := make(map[string]GroupWithMemberInfo, len(groupInfos))
	for _, group := range groupInfos {
		groupInfoMap[group.GroupId] = group
	}

	return &ConvInfoMaps{
		UserInfoMap:     userInfoMap,
		GroupInfoMap:    groupInfoMap,
		FriendRemarkMap: friendRemarkMap,
	}
}

// getConvDisplayInfo 获取会话显示信息（昵称和头像）
func (s *ServerMessage) getConvDisplayInfo(convType int8, targetId string, convInfoMaps *ConvInfoMaps) (string, string) {
	var convNick, convAvatar string

	if convType == consts.ChatMsgType_Private {
		// 单聊：获取好友的昵称和头像，优先使用好友备注
		if targetUser, exists := convInfoMaps.UserInfoMap[targetId]; exists {
			// 优先使用好友备注，如果没有备注则使用用户昵称
			if friendRemark, hasRemark := convInfoMaps.FriendRemarkMap[targetId]; hasRemark && friendRemark != "" {
				convNick = friendRemark
			} else {
				convNick = targetUser.UserNick
			}
			convAvatar = targetUser.UserAvatar
		}
	} else if convType == consts.ChatMsgType_Group {
		// 群聊：获取群名称和头像，优先使用用户备注
		if groupInfo, exists := convInfoMaps.GroupInfoMap[targetId]; exists {
			// 如果用户有群备注，使用备注作为昵称，否则使用群名称
			if groupInfo.Remarks != "" {
				convNick = groupInfo.Remarks
			} else {
				convNick = groupInfo.GroupName
			}
			convAvatar = groupInfo.GroupAvatar
		}
	}

	return convNick, convAvatar
}

// calculateMessageStatus 计算消息状态（统一从ChatMessageReceiver表获取）
func (s *ServerMessage) calculateMessageStatus(senderId, currentUserId string, receiverStatus *int8) int8 {
	// 1. 如果是自己发送的消息
	if senderId == currentUserId {
		// 自己发送的消息，需要查询对方的接收状态
		// 如果没有接收记录，说明对方还未接收，状态为"已发送"
		if receiverStatus == nil {
			return consts.MessageStatus_Sended // 已发送，对方未接收
		}
		return *receiverStatus // 返回对方的接收状态
	}

	// 2. 如果是接收的消息
	// 如果没有自己的接收记录，说明还未读，状态为"未读"
	if receiverStatus == nil {
		return consts.MessageStatus_Sended // 已发送，自己未读
	}
	return *receiverStatus // 返回自己的接收状态
}

// getGroupMessageReceiversStatus 获取群聊消息的所有接收者状态
func (s *ServerMessage) getGroupMessageReceiversStatus(messages []MessageWithReceiver, groupId string) map[string][]chat.MessageReceiverInfo {
	if len(messages) == 0 {
		return nil
	}

	// 1. 收集所有消息ID
	msgIds := make([]string, 0, len(messages))
	for _, msg := range messages {
		msgIds = append(msgIds, msg.MsgId)
	}

	// 2. 批量查询所有消息的接收者状态
	type ReceiverStatusInfo struct {
		MsgId      string      `gorm:"column:msg_id"`
		ReceiverId string      `gorm:"column:receiver_id"`
		Status     int8        `gorm:"column:status"`
		ReadTime   *gtime.Time `gorm:"column:read_time"`
		UserNick   string      `gorm:"column:user_nick"`
		UserAvatar string      `gorm:"column:user_avatar"`
	}

	var receiversStatus []ReceiverStatusInfo
	dao.Db.Table("chat_message_receivers as cmr").
		Select("cmr.msg_id, cmr.receiver_id, cmr.status, cmr.read_time, ui.user_nick, ui.user_avatar").
		Joins("LEFT JOIN user_infos as ui ON cmr.receiver_id = ui.user_id").
		Where("cmr.msg_id IN ?", msgIds).
		Find(&receiversStatus)

	// 3. 获取群成员列表（用于补充没有接收记录的成员）
	var groupMembers []string
	dao.Db.Model(&modelChat.ChatGroupMember{}).
		Select("user_id").
		Where("group_id = ? AND deleted_at IS NULL AND exit_time IS NULL", groupId).
		Pluck("user_id", &groupMembers)

	// 4. 构建结果映射
	result := make(map[string][]chat.MessageReceiverInfo)

	// 按消息ID分组
	receiversByMsg := make(map[string][]ReceiverStatusInfo)
	for _, receiver := range receiversStatus {
		receiversByMsg[receiver.MsgId] = append(receiversByMsg[receiver.MsgId], receiver)
	}

	// 5. 为每条消息构建完整的接收者状态列表
	for _, msg := range messages {
		receivers := make([]chat.MessageReceiverInfo, 0, len(groupMembers))

		// 创建已有接收记录的映射
		existingReceivers := make(map[string]ReceiverStatusInfo)
		if msgReceivers, exists := receiversByMsg[msg.MsgId]; exists {
			for _, receiver := range msgReceivers {
				existingReceivers[receiver.ReceiverId] = receiver
			}
		}

		// 为每个群成员构建接收状态
		for _, memberId := range groupMembers {
			var receiverInfo chat.MessageReceiverInfo

			if receiver, hasRecord := existingReceivers[memberId]; hasRecord {
				// 有接收记录
				receiverInfo = chat.MessageReceiverInfo{
					ReceiverId:     receiver.ReceiverId,
					ReceiverNick:   receiver.UserNick,
					ReceiverAvatar: receiver.UserAvatar,
					Status:         receiver.Status,
					ReadTime:       tools.GtimeToStringNMDHMS(receiver.ReadTime),
				}
			} else {
				// 没有接收记录，默认为已发送状态
				receiverInfo = chat.MessageReceiverInfo{
					ReceiverId: memberId,
					Status:     consts.MessageStatus_Sended, // 已发送，未读
				}
			}

			receivers = append(receivers, receiverInfo)
		}

		result[msg.MsgId] = receivers
	}

	return result
}

// buildMessageInfo 构建MessageInfo对象（GetMsgListForConvId和GetMsgConvList共用）
func (s *ServerMessage) buildMessageInfo(
	msgId, msgType, senderId string,
	sendTime *gtime.Time,
	msgContentFmt int8,
	msgContentType, msgContent string,
	receiverStatus *int8,
	readTime *gtime.Time,
	convId string,
	convType int8,
	userId string,
	userInfoMap map[string]user.UserInfoRes,
	receivers []chat.MessageReceiverInfo,
) chat.MessageInfo {
	// 1. 解析消息内容
	var parsedContent interface{}
	if msgContent != "" {
		if err := json.Unmarshal([]byte(msgContent), &parsedContent); err != nil {
			// 如果解析失败，使用原始字符串
			parsedContent = msgContent
		}
	}

	// 2. 获取发送者信息
	var senderNick, senderAvatar string
	if userInfo, exists := userInfoMap[senderId]; exists {
		senderNick = userInfo.UserNick
		senderAvatar = userInfo.UserAvatar
	}

	// 3. 计算消息状态
	msgStatus := s.calculateMessageStatus(senderId, userId, receiverStatus)

	// 4. 转换发送时间戳（确保与字符串时间一致）
	sendTimestamp := tools.GtimeToTimestamp(sendTime)

	// 5. 构建MessageInfo对象
	messageInfo := chat.MessageInfo{
		MsgType:        msgType,
		MsgId:          msgId,
		MsgStatus:      msgStatus,
		MsgReadTime:    tools.GtimeToStringNMDHMS(readTime),
		MsgContentFmt:  msgContentFmt,
		MsgContentType: msgContentType,
		MsgContent:     parsedContent,
		SendTimestamp:  sendTimestamp,                       // 发送时间戳
		SendTime:       tools.GtimeToStringNMDHMS(sendTime), // 发送时间字符串

		SenderId:     senderId,
		SenderNick:   senderNick,
		SenderAvatar: senderAvatar,
		ConvId:       convId,
		ConvType:     convType,
		Receivers:    receivers, // 群聊接收者状态列表（单聊时为空）
	}

	// 6. 设置是否是自己发送的消息
	if messageInfo.SenderId == userId {
		messageInfo.IsMyself = true
	}

	return messageInfo
}
