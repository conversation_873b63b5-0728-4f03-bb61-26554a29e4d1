//go:build ignore

package main

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
)

// 这个文件用于生成包含外部文档的API定义
// 运行方式: go run generate_docs.go

func main() {
	fmt.Println("开始生成包含外部文档的API定义...")

	// 读取外部文档
	wsLoginDoc, err := readDocFile("docs/ws-login.md")
	if err != nil {
		fmt.Printf("读取ws-login.md失败: %v\n", err)
		return
	}

	testApiDoc, err := readDocFile("docs/test-api.md")
	if err != nil {
		fmt.Printf("读取test-api.md失败: %v\n", err)
		return
	}

	messageStatusDoc, err := readDocFile("docs/message-status.md")
	if err != nil {
		fmt.Printf("读取message-status.md失败: %v\n", err)
		return
	}

	// 转换Markdown为HTML
	wsLoginHTML := markdownToHTML(wsLoginDoc)
	testApiHTML := markdownToHTML(testApiDoc)
	messageStatusHTML := markdownToHTML(messageStatusDoc)

	// 生成API定义代码
	apiCode := generateAPICode(wsLoginHTML, testApiHTML, messageStatusHTML)

	// 写入到新文件
	err = os.WriteFile("chat_with_docs.go", []byte(apiCode), 0644)
	if err != nil {
		fmt.Printf("写入文件失败: %v\n", err)
		return
	}

	fmt.Println("API定义生成完成！")
	fmt.Println("生成的文件: chat_with_docs.go")
	fmt.Println("请将此文件内容复制到 chat.go 中替换相应的结构体定义")
}

// readDocFile 读取文档文件
func readDocFile(filename string) (string, error) {
	content, err := os.ReadFile(filename)
	if err != nil {
		return "", err
	}
	return string(content), nil
}

// markdownToHTML 简单的Markdown到HTML转换
func markdownToHTML(markdown string) string {
	html := markdown

	// 转换标题
	html = strings.ReplaceAll(html, "\n# ", "\n<h3>")
	html = strings.ReplaceAll(html, "\n## ", "\n<h4>")
	html = strings.ReplaceAll(html, "\n### ", "\n<h5>")

	// 处理标题结尾
	lines := strings.Split(html, "\n")
	for i, line := range lines {
		if strings.HasPrefix(line, "<h3>") || strings.HasPrefix(line, "<h4>") || strings.HasPrefix(line, "<h5>") {
			lines[i] = line + strings.Replace(strings.Replace(strings.Replace(line, "<h3>", "</h3>", 1), "<h4>", "</h4>", 1), "<h5>", "</h5>", 1)[len(line):]
		}
	}
	html = strings.Join(lines, "\n")

	// 转换代码块
	html = strings.ReplaceAll(html, "```json", "<pre><code class=\"language-json\">")
	html = strings.ReplaceAll(html, "```javascript", "<pre><code class=\"language-javascript\">")
	html = strings.ReplaceAll(html, "```python", "<pre><code class=\"language-python\">")
	html = strings.ReplaceAll(html, "```", "</code></pre>")

	// 转换行内代码
	html = strings.ReplaceAll(html, "`", "<code>")
	// 注意：这里简化处理，实际应该配对处理

	// 转换粗体
	html = strings.ReplaceAll(html, "**", "<strong>")
	// 注意：这里简化处理，实际应该配对处理

	// 转换列表
	lines = strings.Split(html, "\n")
	inList := false
	for i, line := range lines {
		trimmed := strings.TrimSpace(line)
		if strings.HasPrefix(trimmed, "- ") {
			if !inList {
				lines[i] = "<ul><li>" + trimmed[2:] + "</li>"
				inList = true
			} else {
				lines[i] = "<li>" + trimmed[2:] + "</li>"
			}
		} else if inList && trimmed != "" {
			lines[i-1] = lines[i-1] + "</ul>"
			inList = false
		}
	}
	if inList {
		lines[len(lines)-1] = lines[len(lines)-1] + "</ul>"
	}

	html = strings.Join(lines, "\n")

	// 转换表格（简化处理）
	html = strings.ReplaceAll(html, "|", "</td><td>")
	html = strings.ReplaceAll(html, "\n</td><td>", "\n<tr><td>")
	html = strings.ReplaceAll(html, "<tr><td>---", "<tr><td>") // 移除分隔行

	// 清理和转义
	html = strings.ReplaceAll(html, "\"", "\\\"")
	html = strings.ReplaceAll(html, "\n", "")

	return html
}

// generateAPICode 生成API定义代码
func generateAPICode(wsLoginHTML, testApiHTML, messageStatusHTML string) string {
	return fmt.Sprintf(`package chat

import "github.com/gogf/gf/v2/frame/g"

// ==================== 包含外部文档的API定义 ====================

// 聊天websocket长连接 登录请求 (ws 消息结构体)
type ChatWsLoginReq struct {
	g.Meta `+"`"+`path:"/ws-login" tags:"聊天 WebSocket" method:"get" summary:"1、聊天长连接登录" description:"%s"`+"`"+`

	MsgType         string `+"`"+`json:"msg_type" dc:"ws 消息类型: chat 聊天消息, chat_notice 聊天通知消息, system 系统信息, ping 心跳信息"`+"`"+`
	MsgClientId     string `+"`"+`json:"msg_client_id" dc:"ws 消息发送端生成的消息id,后端不入库(前端自己判断使用)"`+"`"+`
	MsgReceiverId   string `+"`"+`json:"msg_receiver_id" dc:"ws 消息接收端id: 单聊(对方user_id),群聊(group_id)"`+"`"+`
	MsgReceiverType int    `+"`"+`json:"msg_receiver_type" dc:"ws 消息接收端类型: 1 单聊， 2 群聊"`+"`"+`

	MsgContentFmt  int         `+"`"+`json:"msg_content_fmt" dc:"ws 内容格式: 1 json, 2 protobuf"`+"`"+`
	MsgContentType string      `+"`"+`json:"msg_content_type" dc:"ws 内容类型: text，image，audio，video ,file 等(可扩展)"`+"`"+`
	MsgContent     interface{} `+"`"+`json:"msg_content" dc:"ws 消息内容: 可扩展"`+"`"+`
}

// 聊天功能测试接口请求
type ChatTestApiReq struct {
	g.Meta `+"`"+`path:"/test-api" tags:"聊天测试" method:"post" summary:"2、聊天功能测试接口" description:"%s"`+"`"+`

	TestType        string   `+"`"+`json:"test_type" dc:"测试类型" v:"required|in:single_chat,group_chat,message_status,offline_message,performance"`+"`"+`
	SenderId        string   `+"`"+`json:"sender_id" dc:"发送者用户ID" v:"required"`+"`"+`
	ReceiverId      string   `+"`"+`json:"receiver_id" dc:"接收者ID" v:"required"`+"`"+`
	MessageCount    int      `+"`"+`json:"message_count" dc:"测试消息数量，默认10条" d:"10"`+"`"+`
	MessageInterval int      `+"`"+`json:"message_interval" dc:"消息发送间隔（毫秒），默认1000ms" d:"1000"`+"`"+`
	ContentTypes    []string `+"`"+`json:"content_types" dc:"消息内容类型列表" d:"[\"text\"]"`+"`"+`
	AutoRead        bool     `+"`"+`json:"auto_read" dc:"是否自动标记为已读" d:"true"`+"`"+`
	SimulateOffline bool     `+"`"+`json:"simulate_offline" dc:"是否模拟离线场景" d:"false"`+"`"+`
}

// 消息状态查询接口请求
type MessageStatusQueryReq struct {
	g.Meta `+"`"+`path:"/message-status" tags:"消息状态" method:"post" summary:"3、消息状态查询接口" description:"%s"`+"`"+`

	QueryType        string   `+"`"+`json:"query_type" dc:"查询类型" v:"required|in:single,batch,conversation"`+"`"+`
	MsgId            string   `+"`"+`json:"msg_id" dc:"消息ID（单条查询时必填）"`+"`"+`
	MsgIds           []string `+"`"+`json:"msg_ids" dc:"消息ID列表（批量查询时必填）"`+"`"+`
	ConvId           string   `+"`"+`json:"conv_id" dc:"会话ID（会话查询时必填）"`+"`"+`
	StartTime        string   `+"`"+`json:"start_time" dc:"开始时间（会话查询时使用）"`+"`"+`
	EndTime          string   `+"`"+`json:"end_time" dc:"结束时间（会话查询时使用）"`+"`"+`
	IncludeHistory   bool     `+"`"+`json:"include_history" dc:"是否包含状态历史" d:"false"`+"`"+`
	IncludeReceivers bool     `+"`"+`json:"include_receivers" dc:"是否包含接收者详情" d:"true"`+"`"+`
	Limit            int      `+"`"+`json:"limit" dc:"查询数量限制（会话查询时使用）" d:"100"`+"`"+`
}
`, wsLoginHTML, testApiHTML, messageStatusHTML)
}
