package tools

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"io"
	"reflect"
	"regexp"
	"sort"
	"strings"
)

// 验证手机号是否有效
// 支持中国大陆手机号格式验证：
// 1. 以1开头
// 2. 第二位是3-9之间的数字
// 3. 总长度为11位数字
func IsValidPhoneNumber(phone string) bool {
	// 定义中国大陆手机号的正则表达式模式
	pattern := `^1[3-9]\d{9}$`

	// 编译正则表达式
	re, err := regexp.Compile(pattern)
	if err != nil {
		// 若正则表达式编译出错，返回 false
		return false
	}

	// 使用编译好的正则表达式检查输入的手机号是否匹配模式
	return re.MatchString(phone)
}

// 验证有效是否有效
func IsValidEmail(email string) bool {
	// 定义电子邮件的正则表达式模式
	pattern := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
	// 编译正则表达式
	re, err := regexp.Compile(pattern)
	if err != nil {
		// 若正则表达式编译出错，输出错误信息并返回 false
		return false
	}
	// 使用编译好的正则表达式检查输入的电子邮件是否匹配模式
	return re.MatchString(email)
}

// 格式化返回数字,保留两位小数
func FormatNumberTwoDecimal(num float64) string {
	// 如果数字为0，则直接返回0
	if num == 0 {
		return "0"
	}
	// 使用fmt.Sprintf()函数格式化数字，保留两位小数
	return fmt.Sprintf("%.2f", num)
}

// 根据 map 的值进行排序，并返回排序后的键数组 sortType:asc或者desc
func SortMapByValue(m map[string]int, sortType string) []string {
	// 创建一个键值对切片
	type kv struct {
		Key   string
		Value int
	}

	var kvSlice []kv
	for k, v := range m {
		kvSlice = append(kvSlice, kv{k, v})
	}

	// 根据值排序
	sort.Slice(kvSlice, func(i, j int) bool {
		if sortType == "asc" {
			return kvSlice[i].Value < kvSlice[j].Value
		} else {
			return kvSlice[i].Value > kvSlice[j].Value
		}
	})

	// 提取排序后的键
	var sortedKeys []string
	for _, kv := range kvSlice {
		sortedKeys = append(sortedKeys, kv.Key)
	}

	return sortedKeys
}

// 数组去重
func RemoveDuplicates(arr []int) []int {
	// 创建一个map，用于记录数组中的元素是否已经出现过
	seen := make(map[int]bool)
	result := []int{}

	// 遍历数组，将不重复的元素添加到结果数组中
	for _, num := range arr {
		if !seen[num] {
			result = append(result, num)
			seen[num] = true
		}
	}
	sort.Ints(result)
	return result
}

/**
* @Description: 将Go结构体转换为map
* @param: obj  结构体
* @param: excludeFields 过滤掉不返回的字段,比如批量插入时去掉主键 例如:["id""]
 */
func StructToMap(obj interface{}, excludeFields []string) (map[string]interface{}, error) {
	t := reflect.TypeOf(obj)
	v := reflect.ValueOf(obj)
	exclude := make(map[string]struct{}, 0)

	if t.Kind() == reflect.Ptr {
		t = t.Elem()
		v = v.Elem()
	}
	// 排除返回字段
	for _, f := range excludeFields {
		exclude[f] = struct{}{}
	}

	if t.Kind() != reflect.Struct {
		return nil, fmt.Errorf("input must be a struct")
	}

	result := make(map[string]interface{})
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		value := v.Field(i).Interface()

		// 过滤0值
		//if filterEmpty {
		//	isZeroValue := reflect.DeepEqual(value, reflect.Zero(reflect.TypeOf(value)).Interface())
		//	if isZeroValue {
		//		continue
		//	}
		//}

		jsonTag := field.Tag.Get("json")
		if jsonTag == "" {
			jsonTag = field.Name
		}

		// 过滤 不需要的字段
		if _, ok := exclude[jsonTag]; ok {
			continue
		}

		result[jsonTag] = value
	}

	return result, nil
}

/**
* @Author: YJM
* @Description: 比较相同结构的结构体，并返回差异字段(src的值)数据map
* @param: src  对比的数据，最后返回的是这个结构的值
* @param: dst 对比的目标数据
* @param: excludeFields 不需要对比的字段 例如:["id","name"]
* @param: canZeroFields 可以为0值的字段
 */
func CompareStruct(src, dst interface{}, excludeFields, canZeroFields []string) (diff map[string]interface{}) {
	var (
		exclude       = make(map[string]struct{}, 5)
		zeroFieldsMap = make(map[string]bool, 5)
		srcType       reflect.Type
		srcVal        reflect.Value
		dstVal        reflect.Value
	)

	// 不需要比较的字段
	for _, f := range excludeFields {
		exclude[f] = struct{}{}
	}

	// 允许零值的字段
	for _, field := range canZeroFields {
		zeroFieldsMap[field] = true
	}

	switch reflect.TypeOf(src).Kind() {
	case reflect.Ptr:
		srcType = reflect.TypeOf(src).Elem()
		srcVal = reflect.ValueOf(src).Elem()
	case reflect.Struct:
		srcType = reflect.TypeOf(src)
		srcVal = reflect.ValueOf(src)
	default:
		//g.Log().Warning("传入类型必须是结构体或者结构体指针类型")
		return
	}

	switch reflect.TypeOf(dst).Kind() {
	case reflect.Ptr:
		dstVal = reflect.ValueOf(dst).Elem()
	case reflect.Struct:
		dstVal = reflect.ValueOf(dst)
	default:
		//g.Log().Warning("传入类型必须是结构体或者结构体指针类型")
		return
	}

	diff = make(map[string]interface{}, 5)
	for i := 0; i < srcType.NumField(); i++ {
		fieldName := srcType.Field(i).Name

		tag := strings.Split(srcType.Field(i).Tag.Get("json"), ",")
		if len(tag) < 1 || len(tag[0]) < 1 {
			continue
		}

		if srcVal.FieldByName(fieldName).IsZero() && !zeroFieldsMap[tag[0]] { // 零值不对比更新
			continue
		}

		val := dstVal.FieldByName(fieldName)
		if val.Kind() == reflect.Invalid {
			continue
		}

		if _, ok := exclude[tag[0]]; ok {
			continue
		}

		value1 := srcVal.FieldByName(fieldName).Interface()
		value2 := val.Interface()

		if !reflect.DeepEqual(value1, value2) {
			diff[tag[0]] = value1
		}
	}

	return diff
}

/**
* @Author: YJM
* @Description: 判断元素是否在切面里
* @param: slice  切片
* @param: element 目标元素
 */
func ContainsInt(element int, slice []int) bool {
	for _, v := range slice {
		if v == element {
			return true
		}
	}
	return false
}

/**
* @Author: YJM
* @Description: 判断元素是否在切面里
* @param: slice  切片
* @param: element 目标元素
 */
func ContainsString(element string, slice []string) bool {
	for _, v := range slice {
		if v == element {
			return true
		}
	}
	return false
}

/**
* @Author: YJM
* @Description: 截取切片
* @param: arr  需要截取的切片
* @param: start&end  截取切片的起始和结束位置
 */
func SliceArray(arr []int, start, end int) []int {
	if start > len(arr) || end > len(arr) {
		panic("Slice indices out of range")
	}
	return arr[start:end]
}

func ValidateNormalContent(input string) error {
	if input == "" {
		return nil
	}
	// 正则表达式：中文字符、字母、数字、空格、下划线、横线
	isValid := regexp.MustCompile(`^[\p{Han}a-zA-Z0-9_\-\s]+$`).MatchString(input)
	if !isValid {
		return errors.New("输入包含非法字符或SQL攻击字符")
	}

	return nil
}

// SHA256Encrypt 对输入的数据进行 SHA256 加密
func Sha256Encrypt(dataIn string) string {
	// 创建一个新的 SHA256 哈希对象
	hash := sha256.New()
	// 向哈希对象中写入要加密的数据
	hash.Write([]byte(dataIn))
	// 计算哈希值
	hashedBytes := hash.Sum(nil)
	// 将哈希值转换为十六进制字符串
	return hex.EncodeToString(hashedBytes)
}

// 步骤1：错位加密
func shiftEncrypt(s string) string {
	runes := []rune(s)
	for i := 0; i < len(runes); i++ {
		if i%2 == 0 && i+1 < len(runes) {
			// 相邻的字符错位
			runes[i], runes[i+1] = runes[i+1], runes[i]
		}
	}
	return string(runes)
}

// 使用调整后的密钥进行加密
func aesEncrypt(plaintext string, key string) (string, error) {
	block, err := aes.NewCipher(adjustKey(key))
	if err != nil {
		return "", err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}
	// 加密并将 nonce 与密文组合在一起
	ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)
	return base64.URLEncoding.EncodeToString(ciphertext), nil
}

// Base64加密循环6次
func multiEncrypt(content string) string {
	for i := 0; i < 6; i++ {
		content = base64.StdEncoding.EncodeToString([]byte(content))
	}
	return content
}

// 在首尾添加前后缀
func addPrefixSuffix(content string) string {
	return "PREFIX_" + content + "_SUFFIX"
}

// 将 map 序列化为 JSON 字符串
func mapToJSONString(oss map[string]interface{}) (string, error) {
	jsonData, err := json.Marshal(oss)
	if err != nil {
		return "", err
	}
	return string(jsonData), nil
}

func deepCopyMap(original map[string]interface{}) (map[string]interface{}, error) {
	bytes, err := json.Marshal(original)
	if err != nil {
		return nil, err
	}
	// 创建一个新的 map 以存储解码后的数据
	copy := make(map[string]interface{})
	err = json.Unmarshal(bytes, &copy)
	if err != nil {
		return nil, err
	}
	return copy, nil
}

// 封装的加密函数
func EncryptOSS(oss map[string]interface{}, aesKey string) (string, error) {
	// 错位加密
	sendoss, _ := deepCopyMap(oss)
	sendoss["accessKeyId"] = shiftEncrypt(sendoss["accessKeyId"].(string))
	sendoss["accessKeySecret"] = shiftEncrypt(sendoss["accessKeySecret"].(string))
	sendoss["bucket"] = shiftEncrypt(sendoss["bucket"].(string))

	// 序列化为 JSON
	jsonStr, err := mapToJSONString(sendoss)
	if err != nil {
		return "", err
	}

	// Base64 加密并循环 6 次
	encrypted := multiEncrypt(jsonStr)

	//// 添加前后缀
	//encrypted = addPrefixSuffix(encrypted)
	//
	//// AES-GCM 加密  太依赖前端了 前端必须是localhost 或者https才能解密 先放着 在https localhost情况下可以解密 http不行
	//finalEncrypted, err := aesEncrypt(encrypted, aesKey)
	//if err != nil {
	//	return "", err
	//}

	return encrypted, nil
}

// 选择钥长度（16、24 或 32 字节）
const keyLength = 32 // 使用 32 字节的密钥

func adjustKey(key string) []byte {
	// 使用 32 字节的密钥
	keyBytes := []byte(key)
	if len(keyBytes) < keyLength {
		// 如果密钥短于 32 字节，填充到 32 字节
		keyBytes = append(keyBytes, make([]byte, keyLength-len(keyBytes))...)
	} else if len(keyBytes) > keyLength {
		// 如果密钥长于 32 字节，截取到 32 字节
		keyBytes = keyBytes[:keyLength]
	}
	return keyBytes
}

// AES解密
func aesDecrypt(cipherText string, key string) (string, error) {
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", err
	}

	ciphertext, _ := base64.URLEncoding.DecodeString(cipherText)
	iv := ciphertext[:aes.BlockSize]
	ciphertext = ciphertext[aes.BlockSize:]

	stream := cipher.NewCFBDecrypter(block, iv)
	stream.XORKeyStream(ciphertext, ciphertext)

	return string(ciphertext), nil
}

// Base64解密循环6次
func multiDecrypt(content string) (string, error) {
	var err error
	for i := 0; i < 6; i++ {
		data, err := base64.StdEncoding.DecodeString(content)
		if err != nil {
			return "", err
		}
		content = string(data)
	}
	return content, err
}

// 根据上下文获取用户id
func GetUserIdFromCtx(ctx context.Context) (userId string) {
	// 从上下文中获取用户ID
	value := ctx.Value("user_id")
	if value == nil {
		// 尝试从请求上下文中获取
		request := g.RequestFromCtx(ctx)
		if request == nil {
			return ""
		}

		// 从请求上下文变量中获取
		userId = request.GetCtxVar("user_id").String()
		if userId == "" {
			return ""
		}
		return userId
	}

	// 直接从上下文值中获取
	userId, ok := value.(string)
	if !ok {
		return ""
	}

	return userId
}

// 根据上下文获取用户昵称
func GetUserNickFromCtx(ctx context.Context) (userNick string) {
	// 从上下文中获取用户ID
	value := ctx.Value("user_nick")
	if value == nil {
		// 尝试从请求上下文中获取
		request := g.RequestFromCtx(ctx)
		if request == nil {
			return ""
		}

		// 从请求上下文变量中获取
		userNick = request.GetCtxVar("user_nick").String()
		if userNick == "" {
			return ""
		}
		return userNick
	}

	// 直接从上下文值中获取
	userNick, ok := value.(string)
	if !ok {
		return ""
	}

	return userNick
}

// 时间转 字符串
func GtimeToStringNMDHMS(time *gtime.Time) (strRet string) {
	if time != nil {
		strRet = time.Layout("2006-01-02 15:04:05")
	}
	return strRet
}

// 时间转 字符串
func GtimeToStringNMD(time *gtime.Time) (strRet string) {
	if time != nil {
		strRet = time.Layout("2006-01-02")
	}

	return strRet
}

// 字符串时间 转字符串时间 2025-03-12 17:06:25.149498 输入
func StrTimeToStringNMDHMS(strTime string) (strRet string) {
	time, err := gtime.StrToTime(strTime)
	if err != nil {
		return ""
	}

	strRet = time.Layout("2006-01-02 15:04:05")
	return strRet
}

// 字符串时间 转字符串时间 2025-03-12 17:06:25.149498 输入
func StrTimeToStringNMD(strTime string) (strRet string) {
	time, err := gtime.StrToTime(strTime)
	if err != nil {
		return ""
	}

	strRet = time.Layout("2006-01-02")
	return strRet
}
