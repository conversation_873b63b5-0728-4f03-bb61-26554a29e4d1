# 🧪 聊天功能测试接口

此接口用于测试聊天系统的各项功能，包括消息发送、接收、状态更新等。

## 🎯 测试场景
- **单聊测试：** 测试两个用户之间的消息收发
- **群聊测试：** 测试群组内多用户消息广播
- **消息状态测试：** 测试消息的发送、送达、已读状态
- **离线消息测试：** 测试用户离线时的消息存储和推送
- **消息类型测试：** 测试文本、图片、语音、视频等不同类型消息

## 📊 测试数据格式
```json
{
  "test_type": "single_chat",
  "sender_id": "test_user_001",
  "receiver_id": "test_user_002", 
  "message_count": 10,
  "message_interval": 1000,
  "content_types": ["text", "image", "audio"],
  "auto_read": true,
  "simulate_offline": false
}
```

## 📈 测试结果统计
| 指标 | 说明 | 期望值 |
|------|------|--------|
| 发送成功率 | 成功发送的消息数量/总消息数量 | >= 99% |
| 平均响应时间 | 从发送到收到确认的平均时间 | <= 100ms |
| 消息丢失率 | 丢失的消息数量/总消息数量 | <= 0.1% |
| 状态同步准确率 | 状态正确同步的消息数量/总消息数量 | >= 99.9% |

## ⚡ 性能测试参数
- **并发用户数：** 支持1000+并发连接
- **消息吞吐量：** 每秒处理10000+条消息
- **内存使用：** 单连接内存占用<1MB
- **CPU使用率：** 正常负载下<50%

## 🔍 调试信息
测试过程中会输出详细的调试信息，包括：
1. 连接建立和断开日志
2. 消息发送和接收时间戳
3. 错误信息和异常堆栈
4. 性能指标实时统计
5. 内存和CPU使用情况

## 🛠️ 测试工具推荐
- **Postman：** 支持WebSocket测试，可保存测试用例
- **wscat：** 命令行WebSocket客户端，适合自动化测试
- **Artillery：** 专业的WebSocket压力测试工具
- **自定义脚本：** 使用Python/Node.js编写专门的测试脚本

## 📋 测试用例示例

### 单聊测试用例
```json
{
  "test_type": "single_chat",
  "sender_id": "user_001",
  "receiver_id": "user_002",
  "message_count": 50,
  "message_interval": 500,
  "content_types": ["text"],
  "auto_read": false,
  "simulate_offline": false
}
```

### 群聊测试用例
```json
{
  "test_type": "group_chat",
  "sender_id": "user_001",
  "receiver_id": "group_123",
  "message_count": 100,
  "message_interval": 200,
  "content_types": ["text", "image"],
  "auto_read": true,
  "simulate_offline": false
}
```

### 性能测试用例
```json
{
  "test_type": "performance",
  "sender_id": "user_001",
  "receiver_id": "user_002",
  "message_count": 1000,
  "message_interval": 10,
  "content_types": ["text"],
  "auto_read": true,
  "simulate_offline": false
}
```

## 🎯 测试结果示例
```json
{
  "test_id": "test_20250120_001",
  "test_type": "single_chat",
  "start_time": "2025-01-20 10:00:00",
  "end_time": "2025-01-20 10:05:30",
  "duration": 330000,
  "total_messages": 50,
  "success_messages": 50,
  "failed_messages": 0,
  "success_rate": 100.0,
  "avg_response_time": 85.6,
  "max_response_time": 150,
  "min_response_time": 45,
  "message_loss_rate": 0.0,
  "status_sync_rate": 100.0,
  "error_details": [],
  "performance_data": {
    "concurrent_users": 2,
    "message_throughput": 909,
    "memory_usage": 0.8,
    "cpu_usage": 15.2,
    "network_latency": 12.3,
    "connection_count": 2,
    "database_query_time": 5.8,
    "cache_hit_rate": 95.6
  }
}
```

## ⚠️ 注意事项
1. **测试环境：** 建议在独立的测试环境中运行，避免影响生产数据
2. **数据清理：** 测试完成后及时清理测试数据
3. **并发限制：** 注意控制并发数量，避免对系统造成过大压力
4. **监控指标：** 测试过程中密切关注系统资源使用情况
5. **结果分析：** 根据测试结果及时调整系统配置和优化代码
